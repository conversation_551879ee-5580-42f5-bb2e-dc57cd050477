# Cluely AI Assistant Environment Configuration

# Gemini AI API Configuration
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-pro
GEMINI_MAX_TOKENS=1024
GEMINI_TEMPERATURE=0.7

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/cluely
MONGODB_DATABASE=cluely
MONGODB_MAX_POOL_SIZE=10

# Application Configuration
APP_ENV=development
LOG_LEVEL=info
DEBUG_MODE=false

# Security Configuration
ENCRYPTION_KEY=your_32_character_encryption_key_here
SESSION_SECRET=your_session_secret_here

# Capture Configuration
DEFAULT_CAPTURE_INTERVAL=2000
MAX_CAPTURE_SIZE_MB=10
ENABLE_SCREEN_CAPTURE=true
ENABLE_AUDIO_CAPTURE=true
ENABLE_OCR=true

# AI Processing Configuration
AI_PROCESSING_TIMEOUT=30000
MAX_CONTEXT_HISTORY=50
ENABLE_BATCH_PROCESSING=true

# Storage Configuration
DATA_RETENTION_DAYS=30
MAX_SESSION_SIZE_MB=100
ENABLE_DATA_COMPRESSION=true

# Network Configuration
API_TIMEOUT=10000
MAX_RETRIES=3
RETRY_DELAY=1000

# Privacy Configuration
STEALTH_MODE=true
EXCLUDE_FROM_SCREEN_SHARING=true
ENABLE_DATA_ENCRYPTION=true

# Development Configuration (only for development)
VITE_DEV_SERVER_PORT=1420
TAURI_DEV_WATCHER=true
RUST_LOG=info
