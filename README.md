# Cluely AI Assistant

A stealth desktop AI assistant that provides real-time contextual suggestions based on screen content and audio input.

## ⚠️ Important Legal Notice

**This software is provided for educational and research purposes only. Users are solely responsible for ensuring their use of this software complies with all applicable laws, regulations, and ethical guidelines.**

### Ethical Use Requirements

- **Consent**: Only use this software in environments where you have explicit permission to capture screen and audio content
- **Privacy**: Respect the privacy rights of others - do not use this software to capture or analyze content without proper authorization
- **Academic Integrity**: If using in educational settings, ensure compliance with your institution's academic integrity policies
- **Professional Ethics**: In workplace environments, obtain proper authorization and comply with company policies
- **Legal Compliance**: Users must ensure their use complies with local, state, and federal laws regarding privacy, recording, and data collection

### Disclaimer

The developers of this software:
- Are not responsible for any misuse of this software
- Do not endorse or encourage any unethical or illegal use
- Provide this software "as is" without any warranties
- Are not liable for any consequences resulting from the use of this software

**By using this software, you acknowledge that you have read, understood, and agree to these terms and take full responsibility for your use of this software.**

## Features

- **Real-time Screen Capture**: Continuously captures and analyzes screen content using OCR
- **Audio Processing**: Captures and transcribes microphone and system audio
- **AI-Powered Suggestions**: Uses Gemini AI to provide contextual insights and suggestions
- **Stealth Mode**: Operates invisibly with no taskbar presence and protection from screen sharing
- **Global Shortcuts**: System-wide keyboard shortcuts for quick access
- **Session Management**: Comprehensive logging and analysis of all captured data
- **Secure Storage**: Encrypted data storage with MongoDB integration
- **Cross-Platform**: Built with Tauri for macOS, Windows, and Linux support

## Architecture

### Frontend (React + Vite)
- Modern React application with TypeScript support
- Framer Motion for smooth animations
- Zustand for state management
- Translucent overlay UI that's invisible to screen sharing

### Backend (Rust + Tauri)
- High-performance Rust backend for system-level operations
- Real-time screen capture with OCR text extraction
- Audio capture and speech-to-text processing
- Global hotkey management
- Secure data encryption and storage

### AI Integration
- Gemini AI API for contextual analysis
- Real-time processing of screen and audio data
- Intelligent suggestion generation
- Context-aware responses

### Database (MongoDB)
- User profiles and preferences
- Session history and analytics
- Captured data with metadata
- Document storage and indexing

## Installation

### Prerequisites

- Node.js 18+ 
- Rust 1.70+
- MongoDB (local or cloud)
- Gemini AI API key

### Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/cluely-ai-assistant.git
   cd cluely-ai-assistant
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Install Rust dependencies**
   ```bash
   cd src-tauri
   cargo build
   cd ..
   ```

4. **Configure environment**
   Create a `.env` file in the root directory:
   ```env
   GEMINI_API_KEY=your_gemini_api_key_here
   MONGODB_URI=mongodb://localhost:27017/cluely
   ```

5. **Build and run**
   ```bash
   # Development mode
   npm run tauri:dev
   
   # Production build
   npm run tauri:build
   ```

## Configuration

### API Keys

1. **Gemini AI API Key**
   - Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Create a new API key
   - Add to settings panel or environment variables

2. **MongoDB Setup**
   - Install MongoDB locally or use MongoDB Atlas
   - Create a database named "cluely"
   - Update connection string in settings

### Permissions

The application requires several system permissions:

#### macOS
- **Screen Recording**: System Preferences > Security & Privacy > Privacy > Screen Recording
- **Microphone**: System Preferences > Security & Privacy > Privacy > Microphone
- **Accessibility**: System Preferences > Security & Privacy > Privacy > Accessibility

#### Windows
- **Screen Capture**: Usually granted automatically
- **Microphone**: Windows Settings > Privacy > Microphone
- **System Audio**: May require additional setup

#### Linux
- **Screen Capture**: Depends on desktop environment
- **Audio**: PulseAudio or ALSA configuration may be required

## Usage

### Global Shortcuts

- **Cmd/Ctrl + Shift + A**: Toggle overlay visibility
- **Cmd/Ctrl + Shift + L**: Start/stop listening mode
- **Escape**: Hide overlay (when visible)

### Basic Workflow

1. **Launch the application** - It starts in stealth mode (invisible)
2. **Press Cmd+Shift+A** to show the overlay
3. **Click "Listen"** or press Cmd+Shift+L to start capturing
4. **View AI suggestions** in real-time as you work
5. **Copy suggestions** to clipboard with the copy button
6. **Access settings** via the gear icon for configuration

### Features

- **Real-time Analysis**: AI analyzes your screen and audio continuously
- **Contextual Suggestions**: Get relevant insights based on current activity
- **Session History**: Review past sessions and AI responses
- **Smart Summaries**: Automatic session summaries and follow-up suggestions
- **Document Integration**: Upload documents for additional context

## Development

### Project Structure

```
cluely-ai-assistant/
├── src/                    # React frontend
│   ├── components/         # React components
│   ├── hooks/             # Custom React hooks
│   ├── utils/             # Utility functions
│   └── App.jsx            # Main application component
├── src-tauri/             # Rust backend
│   ├── src/               # Rust source code
│   │   ├── screen_capture.rs
│   │   ├── audio_capture.rs
│   │   ├── ai_integration.rs
│   │   ├── database.rs
│   │   ├── overlay.rs
│   │   └── main.rs
│   ├── Cargo.toml         # Rust dependencies
│   └── tauri.conf.json    # Tauri configuration
├── package.json           # Node.js dependencies
└── vite.config.js         # Vite configuration
```

### Adding New Features

1. **Frontend**: Add React components in `src/components/`
2. **Backend**: Add Rust modules in `src-tauri/src/`
3. **API Integration**: Extend `ai_integration.rs`
4. **Database**: Add schemas in `database.rs`

### Testing

```bash
# Run frontend tests
npm test

# Run Rust tests
cd src-tauri
cargo test

# Integration tests
npm run test:integration
```

## Security Considerations

- **Data Encryption**: All captured data is encrypted before storage
- **API Key Security**: API keys are stored securely and never logged
- **Network Security**: All API communications use HTTPS
- **Local Storage**: Sensitive data is encrypted in local storage
- **Memory Protection**: Sensitive data is cleared from memory after use

## Privacy Features

- **Stealth Mode**: Invisible to screen sharing and recording software
- **Data Retention**: Configurable data retention policies
- **Opt-out Options**: Users can disable specific capture types
- **Data Export**: Users can export their data at any time
- **Data Deletion**: Complete data deletion options available

## Troubleshooting

### Common Issues

1. **Permissions Denied**
   - Ensure all required system permissions are granted
   - Restart the application after granting permissions

2. **API Connection Failed**
   - Verify API keys are correct
   - Check internet connection
   - Ensure API quotas are not exceeded

3. **Database Connection Issues**
   - Verify MongoDB is running
   - Check connection string format
   - Ensure database permissions are correct

4. **Audio Capture Not Working**
   - Check microphone permissions
   - Verify audio device selection
   - Test with different audio devices

### Logs and Debugging

- **Application Logs**: Check console output in development mode
- **Rust Logs**: Use `RUST_LOG=debug` environment variable
- **Database Logs**: Check MongoDB logs for connection issues

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

### Code Style

- **Frontend**: ESLint + Prettier configuration
- **Backend**: Rust standard formatting with `cargo fmt`
- **Commits**: Use conventional commit messages

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support, please:
1. Check the troubleshooting section
2. Search existing GitHub issues
3. Create a new issue with detailed information
4. Join our Discord community for real-time help

## Roadmap

- [ ] Windows system audio capture
- [ ] Linux desktop environment integration
- [ ] Local Whisper model support
- [ ] Plugin system for custom AI models
- [ ] Team collaboration features
- [ ] Mobile companion app
- [ ] Advanced analytics dashboard
- [ ] Custom AI model training

---

**Remember: Use this software responsibly and ethically. Always respect privacy rights and obtain proper consent before capturing any content.**
