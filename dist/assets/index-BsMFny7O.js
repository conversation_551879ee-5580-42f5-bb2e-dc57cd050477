(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();function fm(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Pf={exports:{}},ko={},Tf={exports:{}},O={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qr=Symbol.for("react.element"),dm=Symbol.for("react.portal"),pm=Symbol.for("react.fragment"),hm=Symbol.for("react.strict_mode"),mm=Symbol.for("react.profiler"),gm=Symbol.for("react.provider"),ym=Symbol.for("react.context"),vm=Symbol.for("react.forward_ref"),xm=Symbol.for("react.suspense"),Sm=Symbol.for("react.memo"),wm=Symbol.for("react.lazy"),au=Symbol.iterator;function km(e){return e===null||typeof e!="object"?null:(e=au&&e[au]||e["@@iterator"],typeof e=="function"?e:null)}var Ef={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Df=Object.assign,Lf={};function er(e,t,n){this.props=e,this.context=t,this.refs=Lf,this.updater=n||Ef}er.prototype.isReactComponent={};er.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};er.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Af(){}Af.prototype=er.prototype;function Kl(e,t,n){this.props=e,this.context=t,this.refs=Lf,this.updater=n||Ef}var Gl=Kl.prototype=new Af;Gl.constructor=Kl;Df(Gl,er.prototype);Gl.isPureReactComponent=!0;var uu=Array.isArray,Rf=Object.prototype.hasOwnProperty,Ql={current:null},Vf={key:!0,ref:!0,__self:!0,__source:!0};function Mf(e,t,n){var r,i={},o=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(o=""+t.key),t)Rf.call(t,r)&&!Vf.hasOwnProperty(r)&&(i[r]=t[r]);var l=arguments.length-2;if(l===1)i.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];i.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)i[r]===void 0&&(i[r]=l[r]);return{$$typeof:qr,type:e,key:o,ref:s,props:i,_owner:Ql.current}}function Cm(e,t){return{$$typeof:qr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Yl(e){return typeof e=="object"&&e!==null&&e.$$typeof===qr}function Pm(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var cu=/\/+/g;function Qo(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Pm(""+e.key):t.toString(36)}function Ai(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case qr:case dm:s=!0}}if(s)return s=e,i=i(s),e=r===""?"."+Qo(s,0):r,uu(i)?(n="",e!=null&&(n=e.replace(cu,"$&/")+"/"),Ai(i,t,n,"",function(u){return u})):i!=null&&(Yl(i)&&(i=Cm(i,n+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(cu,"$&/")+"/")+e)),t.push(i)),1;if(s=0,r=r===""?".":r+":",uu(e))for(var l=0;l<e.length;l++){o=e[l];var a=r+Qo(o,l);s+=Ai(o,t,n,a,i)}else if(a=km(e),typeof a=="function")for(e=a.call(e),l=0;!(o=e.next()).done;)o=o.value,a=r+Qo(o,l++),s+=Ai(o,t,n,a,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function ai(e,t,n){if(e==null)return e;var r=[],i=0;return Ai(e,r,"","",function(o){return t.call(n,o,i++)}),r}function Tm(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Se={current:null},Ri={transition:null},Em={ReactCurrentDispatcher:Se,ReactCurrentBatchConfig:Ri,ReactCurrentOwner:Ql};function _f(){throw Error("act(...) is not supported in production builds of React.")}O.Children={map:ai,forEach:function(e,t,n){ai(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return ai(e,function(){t++}),t},toArray:function(e){return ai(e,function(t){return t})||[]},only:function(e){if(!Yl(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};O.Component=er;O.Fragment=pm;O.Profiler=mm;O.PureComponent=Kl;O.StrictMode=hm;O.Suspense=xm;O.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Em;O.act=_f;O.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Df({},e.props),i=e.key,o=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,s=Ql.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)Rf.call(t,a)&&!Vf.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:qr,type:e.type,key:i,ref:o,props:r,_owner:s}};O.createContext=function(e){return e={$$typeof:ym,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:gm,_context:e},e.Consumer=e};O.createElement=Mf;O.createFactory=function(e){var t=Mf.bind(null,e);return t.type=e,t};O.createRef=function(){return{current:null}};O.forwardRef=function(e){return{$$typeof:vm,render:e}};O.isValidElement=Yl;O.lazy=function(e){return{$$typeof:wm,_payload:{_status:-1,_result:e},_init:Tm}};O.memo=function(e,t){return{$$typeof:Sm,type:e,compare:t===void 0?null:t}};O.startTransition=function(e){var t=Ri.transition;Ri.transition={};try{e()}finally{Ri.transition=t}};O.unstable_act=_f;O.useCallback=function(e,t){return Se.current.useCallback(e,t)};O.useContext=function(e){return Se.current.useContext(e)};O.useDebugValue=function(){};O.useDeferredValue=function(e){return Se.current.useDeferredValue(e)};O.useEffect=function(e,t){return Se.current.useEffect(e,t)};O.useId=function(){return Se.current.useId()};O.useImperativeHandle=function(e,t,n){return Se.current.useImperativeHandle(e,t,n)};O.useInsertionEffect=function(e,t){return Se.current.useInsertionEffect(e,t)};O.useLayoutEffect=function(e,t){return Se.current.useLayoutEffect(e,t)};O.useMemo=function(e,t){return Se.current.useMemo(e,t)};O.useReducer=function(e,t,n){return Se.current.useReducer(e,t,n)};O.useRef=function(e){return Se.current.useRef(e)};O.useState=function(e){return Se.current.useState(e)};O.useSyncExternalStore=function(e,t,n){return Se.current.useSyncExternalStore(e,t,n)};O.useTransition=function(){return Se.current.useTransition()};O.version="18.3.1";Tf.exports=O;var k=Tf.exports;const Vr=fm(k);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Dm=k,Lm=Symbol.for("react.element"),Am=Symbol.for("react.fragment"),Rm=Object.prototype.hasOwnProperty,Vm=Dm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Mm={key:!0,ref:!0,__self:!0,__source:!0};function Nf(e,t,n){var r,i={},o=null,s=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)Rm.call(t,r)&&!Mm.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Lm,type:e,key:o,ref:s,props:i,_owner:Vm.current}}ko.Fragment=Am;ko.jsx=Nf;ko.jsxs=Nf;Pf.exports=ko;var D=Pf.exports,Os={},jf={exports:{}},Ne={},Of={exports:{}},If={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(L,_){var j=L.length;L.push(_);e:for(;0<j;){var N=j-1>>>1,H=L[N];if(0<i(H,_))L[N]=_,L[j]=H,j=N;else break e}}function n(L){return L.length===0?null:L[0]}function r(L){if(L.length===0)return null;var _=L[0],j=L.pop();if(j!==_){L[0]=j;e:for(var N=0,H=L.length,Yt=H>>>1;N<Yt;){var qe=2*(N+1)-1,wn=L[qe],Le=qe+1,Xt=L[Le];if(0>i(wn,j))Le<H&&0>i(Xt,wn)?(L[N]=Xt,L[Le]=j,N=Le):(L[N]=wn,L[qe]=j,N=qe);else if(Le<H&&0>i(Xt,j))L[N]=Xt,L[Le]=j,N=Le;else break e}}return _}function i(L,_){var j=L.sortIndex-_.sortIndex;return j!==0?j:L.id-_.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var s=Date,l=s.now();e.unstable_now=function(){return s.now()-l}}var a=[],u=[],f=1,c=null,p=3,m=!1,y=!1,x=!1,C=typeof setTimeout=="function"?setTimeout:null,g=typeof clearTimeout=="function"?clearTimeout:null,d=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function h(L){for(var _=n(u);_!==null;){if(_.callback===null)r(u);else if(_.startTime<=L)r(u),_.sortIndex=_.expirationTime,t(a,_);else break;_=n(u)}}function v(L){if(x=!1,h(L),!y)if(n(a)!==null)y=!0,q(S);else{var _=n(u);_!==null&&Oe(v,_.startTime-L)}}function S(L,_){y=!1,x&&(x=!1,g(P),P=-1),m=!0;var j=p;try{for(h(_),c=n(a);c!==null&&(!(c.expirationTime>_)||L&&!J());){var N=c.callback;if(typeof N=="function"){c.callback=null,p=c.priorityLevel;var H=N(c.expirationTime<=_);_=e.unstable_now(),typeof H=="function"?c.callback=H:c===n(a)&&r(a),h(_)}else r(a);c=n(a)}if(c!==null)var Yt=!0;else{var qe=n(u);qe!==null&&Oe(v,qe.startTime-_),Yt=!1}return Yt}finally{c=null,p=j,m=!1}}var w=!1,E=null,P=-1,R=5,V=-1;function J(){return!(e.unstable_now()-V<R)}function K(){if(E!==null){var L=e.unstable_now();V=L;var _=!0;try{_=E(!0,L)}finally{_?ge():(w=!1,E=null)}}else w=!1}var ge;if(typeof d=="function")ge=function(){d(K)};else if(typeof MessageChannel<"u"){var oe=new MessageChannel,xt=oe.port2;oe.port1.onmessage=K,ge=function(){xt.postMessage(null)}}else ge=function(){C(K,0)};function q(L){E=L,w||(w=!0,ge())}function Oe(L,_){P=C(function(){L(e.unstable_now())},_)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(L){L.callback=null},e.unstable_continueExecution=function(){y||m||(y=!0,q(S))},e.unstable_forceFrameRate=function(L){0>L||125<L?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):R=0<L?Math.floor(1e3/L):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(L){switch(p){case 1:case 2:case 3:var _=3;break;default:_=p}var j=p;p=_;try{return L()}finally{p=j}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(L,_){switch(L){case 1:case 2:case 3:case 4:case 5:break;default:L=3}var j=p;p=L;try{return _()}finally{p=j}},e.unstable_scheduleCallback=function(L,_,j){var N=e.unstable_now();switch(typeof j=="object"&&j!==null?(j=j.delay,j=typeof j=="number"&&0<j?N+j:N):j=N,L){case 1:var H=-1;break;case 2:H=250;break;case 5:H=**********;break;case 4:H=1e4;break;default:H=5e3}return H=j+H,L={id:f++,callback:_,priorityLevel:L,startTime:j,expirationTime:H,sortIndex:-1},j>N?(L.sortIndex=j,t(u,L),n(a)===null&&L===n(u)&&(x?(g(P),P=-1):x=!0,Oe(v,j-N))):(L.sortIndex=H,t(a,L),y||m||(y=!0,q(S))),L},e.unstable_shouldYield=J,e.unstable_wrapCallback=function(L){var _=p;return function(){var j=p;p=_;try{return L.apply(this,arguments)}finally{p=j}}}})(If);Of.exports=If;var _m=Of.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Nm=k,Me=_m;function T(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Ff=new Set,Mr={};function yn(e,t){Kn(e,t),Kn(e+"Capture",t)}function Kn(e,t){for(Mr[e]=t,e=0;e<t.length;e++)Ff.add(t[e])}var pt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Is=Object.prototype.hasOwnProperty,jm=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,fu={},du={};function Om(e){return Is.call(du,e)?!0:Is.call(fu,e)?!1:jm.test(e)?du[e]=!0:(fu[e]=!0,!1)}function Im(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Fm(e,t,n,r){if(t===null||typeof t>"u"||Im(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function we(e,t,n,r,i,o,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=s}var ce={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ce[e]=new we(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ce[t]=new we(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ce[e]=new we(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ce[e]=new we(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ce[e]=new we(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ce[e]=new we(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ce[e]=new we(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ce[e]=new we(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ce[e]=new we(e,5,!1,e.toLowerCase(),null,!1,!1)});var Xl=/[\-:]([a-z])/g;function Zl(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Xl,Zl);ce[t]=new we(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Xl,Zl);ce[t]=new we(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Xl,Zl);ce[t]=new we(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ce[e]=new we(e,1,!1,e.toLowerCase(),null,!1,!1)});ce.xlinkHref=new we("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ce[e]=new we(e,1,!1,e.toLowerCase(),null,!0,!0)});function Jl(e,t,n,r){var i=ce.hasOwnProperty(t)?ce[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Fm(t,n,i,r)&&(n=null),r||i===null?Om(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var vt=Nm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ui=Symbol.for("react.element"),Cn=Symbol.for("react.portal"),Pn=Symbol.for("react.fragment"),ql=Symbol.for("react.strict_mode"),Fs=Symbol.for("react.profiler"),zf=Symbol.for("react.provider"),Bf=Symbol.for("react.context"),bl=Symbol.for("react.forward_ref"),zs=Symbol.for("react.suspense"),Bs=Symbol.for("react.suspense_list"),ea=Symbol.for("react.memo"),kt=Symbol.for("react.lazy"),Uf=Symbol.for("react.offscreen"),pu=Symbol.iterator;function rr(e){return e===null||typeof e!="object"?null:(e=pu&&e[pu]||e["@@iterator"],typeof e=="function"?e:null)}var X=Object.assign,Yo;function pr(e){if(Yo===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Yo=t&&t[1]||""}return`
`+Yo+e}var Xo=!1;function Zo(e,t){if(!e||Xo)return"";Xo=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),o=r.stack.split(`
`),s=i.length-1,l=o.length-1;1<=s&&0<=l&&i[s]!==o[l];)l--;for(;1<=s&&0<=l;s--,l--)if(i[s]!==o[l]){if(s!==1||l!==1)do if(s--,l--,0>l||i[s]!==o[l]){var a=`
`+i[s].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=s&&0<=l);break}}}finally{Xo=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?pr(e):""}function zm(e){switch(e.tag){case 5:return pr(e.type);case 16:return pr("Lazy");case 13:return pr("Suspense");case 19:return pr("SuspenseList");case 0:case 2:case 15:return e=Zo(e.type,!1),e;case 11:return e=Zo(e.type.render,!1),e;case 1:return e=Zo(e.type,!0),e;default:return""}}function Us(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Pn:return"Fragment";case Cn:return"Portal";case Fs:return"Profiler";case ql:return"StrictMode";case zs:return"Suspense";case Bs:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Bf:return(e.displayName||"Context")+".Consumer";case zf:return(e._context.displayName||"Context")+".Provider";case bl:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ea:return t=e.displayName||null,t!==null?t:Us(e.type)||"Memo";case kt:t=e._payload,e=e._init;try{return Us(e(t))}catch{}}return null}function Bm(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Us(t);case 8:return t===ql?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Bt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function $f(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Um(e){var t=$f(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(s){r=""+s,o.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ci(e){e._valueTracker||(e._valueTracker=Um(e))}function Hf(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=$f(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Hi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function $s(e,t){var n=t.checked;return X({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n!=null?n:e._wrapperState.initialChecked})}function hu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Bt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Wf(e,t){t=t.checked,t!=null&&Jl(e,"checked",t,!1)}function Hs(e,t){Wf(e,t);var n=Bt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Ws(e,t.type,n):t.hasOwnProperty("defaultValue")&&Ws(e,t.type,Bt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function mu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Ws(e,t,n){(t!=="number"||Hi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var hr=Array.isArray;function zn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Bt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Ks(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(T(91));return X({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function gu(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(T(92));if(hr(n)){if(1<n.length)throw Error(T(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Bt(n)}}function Kf(e,t){var n=Bt(t.value),r=Bt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function yu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Gf(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Gs(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Gf(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var fi,Qf=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(fi=fi||document.createElement("div"),fi.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=fi.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function _r(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var vr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},$m=["Webkit","ms","Moz","O"];Object.keys(vr).forEach(function(e){$m.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),vr[t]=vr[e]})});function Yf(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||vr.hasOwnProperty(e)&&vr[e]?(""+t).trim():t+"px"}function Xf(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=Yf(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var Hm=X({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Qs(e,t){if(t){if(Hm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(T(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(T(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(T(61))}if(t.style!=null&&typeof t.style!="object")throw Error(T(62))}}function Ys(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Xs=null;function ta(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Zs=null,Bn=null,Un=null;function vu(e){if(e=ti(e)){if(typeof Zs!="function")throw Error(T(280));var t=e.stateNode;t&&(t=Do(t),Zs(e.stateNode,e.type,t))}}function Zf(e){Bn?Un?Un.push(e):Un=[e]:Bn=e}function Jf(){if(Bn){var e=Bn,t=Un;if(Un=Bn=null,vu(e),t)for(e=0;e<t.length;e++)vu(t[e])}}function qf(e,t){return e(t)}function bf(){}var Jo=!1;function ed(e,t,n){if(Jo)return e(t,n);Jo=!0;try{return qf(e,t,n)}finally{Jo=!1,(Bn!==null||Un!==null)&&(bf(),Jf())}}function Nr(e,t){var n=e.stateNode;if(n===null)return null;var r=Do(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(T(231,t,typeof n));return n}var Js=!1;if(pt)try{var ir={};Object.defineProperty(ir,"passive",{get:function(){Js=!0}}),window.addEventListener("test",ir,ir),window.removeEventListener("test",ir,ir)}catch{Js=!1}function Wm(e,t,n,r,i,o,s,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(f){this.onError(f)}}var xr=!1,Wi=null,Ki=!1,qs=null,Km={onError:function(e){xr=!0,Wi=e}};function Gm(e,t,n,r,i,o,s,l,a){xr=!1,Wi=null,Wm.apply(Km,arguments)}function Qm(e,t,n,r,i,o,s,l,a){if(Gm.apply(this,arguments),xr){if(xr){var u=Wi;xr=!1,Wi=null}else throw Error(T(198));Ki||(Ki=!0,qs=u)}}function vn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function td(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function xu(e){if(vn(e)!==e)throw Error(T(188))}function Ym(e){var t=e.alternate;if(!t){if(t=vn(e),t===null)throw Error(T(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return xu(i),e;if(o===r)return xu(i),t;o=o.sibling}throw Error(T(188))}if(n.return!==r.return)n=i,r=o;else{for(var s=!1,l=i.child;l;){if(l===n){s=!0,n=i,r=o;break}if(l===r){s=!0,r=i,n=o;break}l=l.sibling}if(!s){for(l=o.child;l;){if(l===n){s=!0,n=o,r=i;break}if(l===r){s=!0,r=o,n=i;break}l=l.sibling}if(!s)throw Error(T(189))}}if(n.alternate!==r)throw Error(T(190))}if(n.tag!==3)throw Error(T(188));return n.stateNode.current===n?e:t}function nd(e){return e=Ym(e),e!==null?rd(e):null}function rd(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=rd(e);if(t!==null)return t;e=e.sibling}return null}var id=Me.unstable_scheduleCallback,Su=Me.unstable_cancelCallback,Xm=Me.unstable_shouldYield,Zm=Me.unstable_requestPaint,b=Me.unstable_now,Jm=Me.unstable_getCurrentPriorityLevel,na=Me.unstable_ImmediatePriority,od=Me.unstable_UserBlockingPriority,Gi=Me.unstable_NormalPriority,qm=Me.unstable_LowPriority,sd=Me.unstable_IdlePriority,Co=null,nt=null;function bm(e){if(nt&&typeof nt.onCommitFiberRoot=="function")try{nt.onCommitFiberRoot(Co,e,void 0,(e.current.flags&128)===128)}catch{}}var Xe=Math.clz32?Math.clz32:ng,eg=Math.log,tg=Math.LN2;function ng(e){return e>>>=0,e===0?32:31-(eg(e)/tg|0)|0}var di=64,pi=4194304;function mr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Qi(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,s=n&268435455;if(s!==0){var l=s&~i;l!==0?r=mr(l):(o&=s,o!==0&&(r=mr(o)))}else s=n&~i,s!==0?r=mr(s):o!==0&&(r=mr(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Xe(t),i=1<<n,r|=e[n],t&=~i;return r}function rg(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ig(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var s=31-Xe(o),l=1<<s,a=i[s];a===-1?(!(l&n)||l&r)&&(i[s]=rg(l,t)):a<=t&&(e.expiredLanes|=l),o&=~l}}function bs(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function ld(){var e=di;return di<<=1,!(di&4194240)&&(di=64),e}function qo(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function br(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Xe(t),e[t]=n}function og(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-Xe(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function ra(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Xe(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var F=0;function ad(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var ud,ia,cd,fd,dd,el=!1,hi=[],Vt=null,Mt=null,_t=null,jr=new Map,Or=new Map,Tt=[],sg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function wu(e,t){switch(e){case"focusin":case"focusout":Vt=null;break;case"dragenter":case"dragleave":Mt=null;break;case"mouseover":case"mouseout":_t=null;break;case"pointerover":case"pointerout":jr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Or.delete(t.pointerId)}}function or(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=ti(t),t!==null&&ia(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function lg(e,t,n,r,i){switch(t){case"focusin":return Vt=or(Vt,e,t,n,r,i),!0;case"dragenter":return Mt=or(Mt,e,t,n,r,i),!0;case"mouseover":return _t=or(_t,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return jr.set(o,or(jr.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,Or.set(o,or(Or.get(o)||null,e,t,n,r,i)),!0}return!1}function pd(e){var t=on(e.target);if(t!==null){var n=vn(t);if(n!==null){if(t=n.tag,t===13){if(t=td(n),t!==null){e.blockedOn=t,dd(e.priority,function(){cd(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Vi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=tl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Xs=r,n.target.dispatchEvent(r),Xs=null}else return t=ti(n),t!==null&&ia(t),e.blockedOn=n,!1;t.shift()}return!0}function ku(e,t,n){Vi(e)&&n.delete(t)}function ag(){el=!1,Vt!==null&&Vi(Vt)&&(Vt=null),Mt!==null&&Vi(Mt)&&(Mt=null),_t!==null&&Vi(_t)&&(_t=null),jr.forEach(ku),Or.forEach(ku)}function sr(e,t){e.blockedOn===t&&(e.blockedOn=null,el||(el=!0,Me.unstable_scheduleCallback(Me.unstable_NormalPriority,ag)))}function Ir(e){function t(i){return sr(i,e)}if(0<hi.length){sr(hi[0],e);for(var n=1;n<hi.length;n++){var r=hi[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Vt!==null&&sr(Vt,e),Mt!==null&&sr(Mt,e),_t!==null&&sr(_t,e),jr.forEach(t),Or.forEach(t),n=0;n<Tt.length;n++)r=Tt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Tt.length&&(n=Tt[0],n.blockedOn===null);)pd(n),n.blockedOn===null&&Tt.shift()}var $n=vt.ReactCurrentBatchConfig,Yi=!0;function ug(e,t,n,r){var i=F,o=$n.transition;$n.transition=null;try{F=1,oa(e,t,n,r)}finally{F=i,$n.transition=o}}function cg(e,t,n,r){var i=F,o=$n.transition;$n.transition=null;try{F=4,oa(e,t,n,r)}finally{F=i,$n.transition=o}}function oa(e,t,n,r){if(Yi){var i=tl(e,t,n,r);if(i===null)as(e,t,r,Xi,n),wu(e,r);else if(lg(i,e,t,n,r))r.stopPropagation();else if(wu(e,r),t&4&&-1<sg.indexOf(e)){for(;i!==null;){var o=ti(i);if(o!==null&&ud(o),o=tl(e,t,n,r),o===null&&as(e,t,r,Xi,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else as(e,t,r,null,n)}}var Xi=null;function tl(e,t,n,r){if(Xi=null,e=ta(r),e=on(e),e!==null)if(t=vn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=td(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Xi=e,null}function hd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Jm()){case na:return 1;case od:return 4;case Gi:case qm:return 16;case sd:return 536870912;default:return 16}default:return 16}}var Dt=null,sa=null,Mi=null;function md(){if(Mi)return Mi;var e,t=sa,n=t.length,r,i="value"in Dt?Dt.value:Dt.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===i[o-r];r++);return Mi=i.slice(e,1<r?1-r:void 0)}function _i(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function mi(){return!0}function Cu(){return!1}function je(e){function t(n,r,i,o,s){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=s,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(o):o[l]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?mi:Cu,this.isPropagationStopped=Cu,this}return X(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=mi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=mi)},persist:function(){},isPersistent:mi}),t}var tr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},la=je(tr),ei=X({},tr,{view:0,detail:0}),fg=je(ei),bo,es,lr,Po=X({},ei,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:aa,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==lr&&(lr&&e.type==="mousemove"?(bo=e.screenX-lr.screenX,es=e.screenY-lr.screenY):es=bo=0,lr=e),bo)},movementY:function(e){return"movementY"in e?e.movementY:es}}),Pu=je(Po),dg=X({},Po,{dataTransfer:0}),pg=je(dg),hg=X({},ei,{relatedTarget:0}),ts=je(hg),mg=X({},tr,{animationName:0,elapsedTime:0,pseudoElement:0}),gg=je(mg),yg=X({},tr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),vg=je(yg),xg=X({},tr,{data:0}),Tu=je(xg),Sg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},wg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},kg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Cg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=kg[e])?!!t[e]:!1}function aa(){return Cg}var Pg=X({},ei,{key:function(e){if(e.key){var t=Sg[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=_i(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?wg[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:aa,charCode:function(e){return e.type==="keypress"?_i(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?_i(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Tg=je(Pg),Eg=X({},Po,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Eu=je(Eg),Dg=X({},ei,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:aa}),Lg=je(Dg),Ag=X({},tr,{propertyName:0,elapsedTime:0,pseudoElement:0}),Rg=je(Ag),Vg=X({},Po,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Mg=je(Vg),_g=[9,13,27,32],ua=pt&&"CompositionEvent"in window,Sr=null;pt&&"documentMode"in document&&(Sr=document.documentMode);var Ng=pt&&"TextEvent"in window&&!Sr,gd=pt&&(!ua||Sr&&8<Sr&&11>=Sr),Du=" ",Lu=!1;function yd(e,t){switch(e){case"keyup":return _g.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function vd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Tn=!1;function jg(e,t){switch(e){case"compositionend":return vd(t);case"keypress":return t.which!==32?null:(Lu=!0,Du);case"textInput":return e=t.data,e===Du&&Lu?null:e;default:return null}}function Og(e,t){if(Tn)return e==="compositionend"||!ua&&yd(e,t)?(e=md(),Mi=sa=Dt=null,Tn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return gd&&t.locale!=="ko"?null:t.data;default:return null}}var Ig={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Au(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Ig[e.type]:t==="textarea"}function xd(e,t,n,r){Zf(r),t=Zi(t,"onChange"),0<t.length&&(n=new la("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var wr=null,Fr=null;function Fg(e){Rd(e,0)}function To(e){var t=Ln(e);if(Hf(t))return e}function zg(e,t){if(e==="change")return t}var Sd=!1;if(pt){var ns;if(pt){var rs="oninput"in document;if(!rs){var Ru=document.createElement("div");Ru.setAttribute("oninput","return;"),rs=typeof Ru.oninput=="function"}ns=rs}else ns=!1;Sd=ns&&(!document.documentMode||9<document.documentMode)}function Vu(){wr&&(wr.detachEvent("onpropertychange",wd),Fr=wr=null)}function wd(e){if(e.propertyName==="value"&&To(Fr)){var t=[];xd(t,Fr,e,ta(e)),ed(Fg,t)}}function Bg(e,t,n){e==="focusin"?(Vu(),wr=t,Fr=n,wr.attachEvent("onpropertychange",wd)):e==="focusout"&&Vu()}function Ug(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return To(Fr)}function $g(e,t){if(e==="click")return To(t)}function Hg(e,t){if(e==="input"||e==="change")return To(t)}function Wg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Je=typeof Object.is=="function"?Object.is:Wg;function zr(e,t){if(Je(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!Is.call(t,i)||!Je(e[i],t[i]))return!1}return!0}function Mu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function _u(e,t){var n=Mu(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Mu(n)}}function kd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?kd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Cd(){for(var e=window,t=Hi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Hi(e.document)}return t}function ca(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Kg(e){var t=Cd(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&kd(n.ownerDocument.documentElement,n)){if(r!==null&&ca(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=_u(n,o);var s=_u(n,r);i&&s&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Gg=pt&&"documentMode"in document&&11>=document.documentMode,En=null,nl=null,kr=null,rl=!1;function Nu(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;rl||En==null||En!==Hi(r)||(r=En,"selectionStart"in r&&ca(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),kr&&zr(kr,r)||(kr=r,r=Zi(nl,"onSelect"),0<r.length&&(t=new la("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=En)))}function gi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Dn={animationend:gi("Animation","AnimationEnd"),animationiteration:gi("Animation","AnimationIteration"),animationstart:gi("Animation","AnimationStart"),transitionend:gi("Transition","TransitionEnd")},is={},Pd={};pt&&(Pd=document.createElement("div").style,"AnimationEvent"in window||(delete Dn.animationend.animation,delete Dn.animationiteration.animation,delete Dn.animationstart.animation),"TransitionEvent"in window||delete Dn.transitionend.transition);function Eo(e){if(is[e])return is[e];if(!Dn[e])return e;var t=Dn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Pd)return is[e]=t[n];return e}var Td=Eo("animationend"),Ed=Eo("animationiteration"),Dd=Eo("animationstart"),Ld=Eo("transitionend"),Ad=new Map,ju="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Wt(e,t){Ad.set(e,t),yn(t,[e])}for(var os=0;os<ju.length;os++){var ss=ju[os],Qg=ss.toLowerCase(),Yg=ss[0].toUpperCase()+ss.slice(1);Wt(Qg,"on"+Yg)}Wt(Td,"onAnimationEnd");Wt(Ed,"onAnimationIteration");Wt(Dd,"onAnimationStart");Wt("dblclick","onDoubleClick");Wt("focusin","onFocus");Wt("focusout","onBlur");Wt(Ld,"onTransitionEnd");Kn("onMouseEnter",["mouseout","mouseover"]);Kn("onMouseLeave",["mouseout","mouseover"]);Kn("onPointerEnter",["pointerout","pointerover"]);Kn("onPointerLeave",["pointerout","pointerover"]);yn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));yn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));yn("onBeforeInput",["compositionend","keypress","textInput","paste"]);yn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));yn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));yn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var gr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Xg=new Set("cancel close invalid load scroll toggle".split(" ").concat(gr));function Ou(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Qm(r,t,void 0,e),e.currentTarget=null}function Rd(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var s=r.length-1;0<=s;s--){var l=r[s],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==o&&i.isPropagationStopped())break e;Ou(i,l,u),o=a}else for(s=0;s<r.length;s++){if(l=r[s],a=l.instance,u=l.currentTarget,l=l.listener,a!==o&&i.isPropagationStopped())break e;Ou(i,l,u),o=a}}}if(Ki)throw e=qs,Ki=!1,qs=null,e}function U(e,t){var n=t[al];n===void 0&&(n=t[al]=new Set);var r=e+"__bubble";n.has(r)||(Vd(t,e,2,!1),n.add(r))}function ls(e,t,n){var r=0;t&&(r|=4),Vd(n,e,r,t)}var yi="_reactListening"+Math.random().toString(36).slice(2);function Br(e){if(!e[yi]){e[yi]=!0,Ff.forEach(function(n){n!=="selectionchange"&&(Xg.has(n)||ls(n,!1,e),ls(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[yi]||(t[yi]=!0,ls("selectionchange",!1,t))}}function Vd(e,t,n,r){switch(hd(t)){case 1:var i=ug;break;case 4:i=cg;break;default:i=oa}n=i.bind(null,t,n,e),i=void 0,!Js||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function as(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var l=r.stateNode.containerInfo;if(l===i||l.nodeType===8&&l.parentNode===i)break;if(s===4)for(s=r.return;s!==null;){var a=s.tag;if((a===3||a===4)&&(a=s.stateNode.containerInfo,a===i||a.nodeType===8&&a.parentNode===i))return;s=s.return}for(;l!==null;){if(s=on(l),s===null)return;if(a=s.tag,a===5||a===6){r=o=s;continue e}l=l.parentNode}}r=r.return}ed(function(){var u=o,f=ta(n),c=[];e:{var p=Ad.get(e);if(p!==void 0){var m=la,y=e;switch(e){case"keypress":if(_i(n)===0)break e;case"keydown":case"keyup":m=Tg;break;case"focusin":y="focus",m=ts;break;case"focusout":y="blur",m=ts;break;case"beforeblur":case"afterblur":m=ts;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=Pu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=pg;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=Lg;break;case Td:case Ed:case Dd:m=gg;break;case Ld:m=Rg;break;case"scroll":m=fg;break;case"wheel":m=Mg;break;case"copy":case"cut":case"paste":m=vg;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=Eu}var x=(t&4)!==0,C=!x&&e==="scroll",g=x?p!==null?p+"Capture":null:p;x=[];for(var d=u,h;d!==null;){h=d;var v=h.stateNode;if(h.tag===5&&v!==null&&(h=v,g!==null&&(v=Nr(d,g),v!=null&&x.push(Ur(d,v,h)))),C)break;d=d.return}0<x.length&&(p=new m(p,y,null,n,f),c.push({event:p,listeners:x}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",m=e==="mouseout"||e==="pointerout",p&&n!==Xs&&(y=n.relatedTarget||n.fromElement)&&(on(y)||y[ht]))break e;if((m||p)&&(p=f.window===f?f:(p=f.ownerDocument)?p.defaultView||p.parentWindow:window,m?(y=n.relatedTarget||n.toElement,m=u,y=y?on(y):null,y!==null&&(C=vn(y),y!==C||y.tag!==5&&y.tag!==6)&&(y=null)):(m=null,y=u),m!==y)){if(x=Pu,v="onMouseLeave",g="onMouseEnter",d="mouse",(e==="pointerout"||e==="pointerover")&&(x=Eu,v="onPointerLeave",g="onPointerEnter",d="pointer"),C=m==null?p:Ln(m),h=y==null?p:Ln(y),p=new x(v,d+"leave",m,n,f),p.target=C,p.relatedTarget=h,v=null,on(f)===u&&(x=new x(g,d+"enter",y,n,f),x.target=h,x.relatedTarget=C,v=x),C=v,m&&y)t:{for(x=m,g=y,d=0,h=x;h;h=kn(h))d++;for(h=0,v=g;v;v=kn(v))h++;for(;0<d-h;)x=kn(x),d--;for(;0<h-d;)g=kn(g),h--;for(;d--;){if(x===g||g!==null&&x===g.alternate)break t;x=kn(x),g=kn(g)}x=null}else x=null;m!==null&&Iu(c,p,m,x,!1),y!==null&&C!==null&&Iu(c,C,y,x,!0)}}e:{if(p=u?Ln(u):window,m=p.nodeName&&p.nodeName.toLowerCase(),m==="select"||m==="input"&&p.type==="file")var S=zg;else if(Au(p))if(Sd)S=Hg;else{S=Ug;var w=Bg}else(m=p.nodeName)&&m.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(S=$g);if(S&&(S=S(e,u))){xd(c,S,n,f);break e}w&&w(e,p,u),e==="focusout"&&(w=p._wrapperState)&&w.controlled&&p.type==="number"&&Ws(p,"number",p.value)}switch(w=u?Ln(u):window,e){case"focusin":(Au(w)||w.contentEditable==="true")&&(En=w,nl=u,kr=null);break;case"focusout":kr=nl=En=null;break;case"mousedown":rl=!0;break;case"contextmenu":case"mouseup":case"dragend":rl=!1,Nu(c,n,f);break;case"selectionchange":if(Gg)break;case"keydown":case"keyup":Nu(c,n,f)}var E;if(ua)e:{switch(e){case"compositionstart":var P="onCompositionStart";break e;case"compositionend":P="onCompositionEnd";break e;case"compositionupdate":P="onCompositionUpdate";break e}P=void 0}else Tn?yd(e,n)&&(P="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(P="onCompositionStart");P&&(gd&&n.locale!=="ko"&&(Tn||P!=="onCompositionStart"?P==="onCompositionEnd"&&Tn&&(E=md()):(Dt=f,sa="value"in Dt?Dt.value:Dt.textContent,Tn=!0)),w=Zi(u,P),0<w.length&&(P=new Tu(P,e,null,n,f),c.push({event:P,listeners:w}),E?P.data=E:(E=vd(n),E!==null&&(P.data=E)))),(E=Ng?jg(e,n):Og(e,n))&&(u=Zi(u,"onBeforeInput"),0<u.length&&(f=new Tu("onBeforeInput","beforeinput",null,n,f),c.push({event:f,listeners:u}),f.data=E))}Rd(c,t)})}function Ur(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Zi(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=Nr(e,n),o!=null&&r.unshift(Ur(e,o,i)),o=Nr(e,t),o!=null&&r.push(Ur(e,o,i))),e=e.return}return r}function kn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Iu(e,t,n,r,i){for(var o=t._reactName,s=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,i?(a=Nr(n,o),a!=null&&s.unshift(Ur(n,a,l))):i||(a=Nr(n,o),a!=null&&s.push(Ur(n,a,l)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var Zg=/\r\n?/g,Jg=/\u0000|\uFFFD/g;function Fu(e){return(typeof e=="string"?e:""+e).replace(Zg,`
`).replace(Jg,"")}function vi(e,t,n){if(t=Fu(t),Fu(e)!==t&&n)throw Error(T(425))}function Ji(){}var il=null,ol=null;function sl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var ll=typeof setTimeout=="function"?setTimeout:void 0,qg=typeof clearTimeout=="function"?clearTimeout:void 0,zu=typeof Promise=="function"?Promise:void 0,bg=typeof queueMicrotask=="function"?queueMicrotask:typeof zu<"u"?function(e){return zu.resolve(null).then(e).catch(ey)}:ll;function ey(e){setTimeout(function(){throw e})}function us(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Ir(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Ir(t)}function Nt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Bu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var nr=Math.random().toString(36).slice(2),tt="__reactFiber$"+nr,$r="__reactProps$"+nr,ht="__reactContainer$"+nr,al="__reactEvents$"+nr,ty="__reactListeners$"+nr,ny="__reactHandles$"+nr;function on(e){var t=e[tt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ht]||n[tt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Bu(e);e!==null;){if(n=e[tt])return n;e=Bu(e)}return t}e=n,n=e.parentNode}return null}function ti(e){return e=e[tt]||e[ht],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Ln(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(T(33))}function Do(e){return e[$r]||null}var ul=[],An=-1;function Kt(e){return{current:e}}function $(e){0>An||(e.current=ul[An],ul[An]=null,An--)}function z(e,t){An++,ul[An]=e.current,e.current=t}var Ut={},me=Kt(Ut),Pe=Kt(!1),dn=Ut;function Gn(e,t){var n=e.type.contextTypes;if(!n)return Ut;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Te(e){return e=e.childContextTypes,e!=null}function qi(){$(Pe),$(me)}function Uu(e,t,n){if(me.current!==Ut)throw Error(T(168));z(me,t),z(Pe,n)}function Md(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(T(108,Bm(e)||"Unknown",i));return X({},n,r)}function bi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ut,dn=me.current,z(me,e),z(Pe,Pe.current),!0}function $u(e,t,n){var r=e.stateNode;if(!r)throw Error(T(169));n?(e=Md(e,t,dn),r.__reactInternalMemoizedMergedChildContext=e,$(Pe),$(me),z(me,e)):$(Pe),z(Pe,n)}var st=null,Lo=!1,cs=!1;function _d(e){st===null?st=[e]:st.push(e)}function ry(e){Lo=!0,_d(e)}function Gt(){if(!cs&&st!==null){cs=!0;var e=0,t=F;try{var n=st;for(F=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}st=null,Lo=!1}catch(i){throw st!==null&&(st=st.slice(e+1)),id(na,Gt),i}finally{F=t,cs=!1}}return null}var Rn=[],Vn=0,eo=null,to=0,ze=[],Be=0,pn=null,lt=1,at="";function qt(e,t){Rn[Vn++]=to,Rn[Vn++]=eo,eo=e,to=t}function Nd(e,t,n){ze[Be++]=lt,ze[Be++]=at,ze[Be++]=pn,pn=e;var r=lt;e=at;var i=32-Xe(r)-1;r&=~(1<<i),n+=1;var o=32-Xe(t)+i;if(30<o){var s=i-i%5;o=(r&(1<<s)-1).toString(32),r>>=s,i-=s,lt=1<<32-Xe(t)+i|n<<i|r,at=o+e}else lt=1<<o|n<<i|r,at=e}function fa(e){e.return!==null&&(qt(e,1),Nd(e,1,0))}function da(e){for(;e===eo;)eo=Rn[--Vn],Rn[Vn]=null,to=Rn[--Vn],Rn[Vn]=null;for(;e===pn;)pn=ze[--Be],ze[Be]=null,at=ze[--Be],ze[Be]=null,lt=ze[--Be],ze[Be]=null}var Ve=null,Re=null,W=!1,Ye=null;function jd(e,t){var n=Ue(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Hu(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ve=e,Re=Nt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ve=e,Re=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=pn!==null?{id:lt,overflow:at}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ue(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ve=e,Re=null,!0):!1;default:return!1}}function cl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function fl(e){if(W){var t=Re;if(t){var n=t;if(!Hu(e,t)){if(cl(e))throw Error(T(418));t=Nt(n.nextSibling);var r=Ve;t&&Hu(e,t)?jd(r,n):(e.flags=e.flags&-4097|2,W=!1,Ve=e)}}else{if(cl(e))throw Error(T(418));e.flags=e.flags&-4097|2,W=!1,Ve=e}}}function Wu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ve=e}function xi(e){if(e!==Ve)return!1;if(!W)return Wu(e),W=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!sl(e.type,e.memoizedProps)),t&&(t=Re)){if(cl(e))throw Od(),Error(T(418));for(;t;)jd(e,t),t=Nt(t.nextSibling)}if(Wu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(T(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Re=Nt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Re=null}}else Re=Ve?Nt(e.stateNode.nextSibling):null;return!0}function Od(){for(var e=Re;e;)e=Nt(e.nextSibling)}function Qn(){Re=Ve=null,W=!1}function pa(e){Ye===null?Ye=[e]:Ye.push(e)}var iy=vt.ReactCurrentBatchConfig;function ar(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(T(309));var r=n.stateNode}if(!r)throw Error(T(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(s){var l=i.refs;s===null?delete l[o]:l[o]=s},t._stringRef=o,t)}if(typeof e!="string")throw Error(T(284));if(!n._owner)throw Error(T(290,e))}return e}function Si(e,t){throw e=Object.prototype.toString.call(t),Error(T(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Ku(e){var t=e._init;return t(e._payload)}function Id(e){function t(g,d){if(e){var h=g.deletions;h===null?(g.deletions=[d],g.flags|=16):h.push(d)}}function n(g,d){if(!e)return null;for(;d!==null;)t(g,d),d=d.sibling;return null}function r(g,d){for(g=new Map;d!==null;)d.key!==null?g.set(d.key,d):g.set(d.index,d),d=d.sibling;return g}function i(g,d){return g=Ft(g,d),g.index=0,g.sibling=null,g}function o(g,d,h){return g.index=h,e?(h=g.alternate,h!==null?(h=h.index,h<d?(g.flags|=2,d):h):(g.flags|=2,d)):(g.flags|=1048576,d)}function s(g){return e&&g.alternate===null&&(g.flags|=2),g}function l(g,d,h,v){return d===null||d.tag!==6?(d=ys(h,g.mode,v),d.return=g,d):(d=i(d,h),d.return=g,d)}function a(g,d,h,v){var S=h.type;return S===Pn?f(g,d,h.props.children,v,h.key):d!==null&&(d.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===kt&&Ku(S)===d.type)?(v=i(d,h.props),v.ref=ar(g,d,h),v.return=g,v):(v=Bi(h.type,h.key,h.props,null,g.mode,v),v.ref=ar(g,d,h),v.return=g,v)}function u(g,d,h,v){return d===null||d.tag!==4||d.stateNode.containerInfo!==h.containerInfo||d.stateNode.implementation!==h.implementation?(d=vs(h,g.mode,v),d.return=g,d):(d=i(d,h.children||[]),d.return=g,d)}function f(g,d,h,v,S){return d===null||d.tag!==7?(d=cn(h,g.mode,v,S),d.return=g,d):(d=i(d,h),d.return=g,d)}function c(g,d,h){if(typeof d=="string"&&d!==""||typeof d=="number")return d=ys(""+d,g.mode,h),d.return=g,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case ui:return h=Bi(d.type,d.key,d.props,null,g.mode,h),h.ref=ar(g,null,d),h.return=g,h;case Cn:return d=vs(d,g.mode,h),d.return=g,d;case kt:var v=d._init;return c(g,v(d._payload),h)}if(hr(d)||rr(d))return d=cn(d,g.mode,h,null),d.return=g,d;Si(g,d)}return null}function p(g,d,h,v){var S=d!==null?d.key:null;if(typeof h=="string"&&h!==""||typeof h=="number")return S!==null?null:l(g,d,""+h,v);if(typeof h=="object"&&h!==null){switch(h.$$typeof){case ui:return h.key===S?a(g,d,h,v):null;case Cn:return h.key===S?u(g,d,h,v):null;case kt:return S=h._init,p(g,d,S(h._payload),v)}if(hr(h)||rr(h))return S!==null?null:f(g,d,h,v,null);Si(g,h)}return null}function m(g,d,h,v,S){if(typeof v=="string"&&v!==""||typeof v=="number")return g=g.get(h)||null,l(d,g,""+v,S);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case ui:return g=g.get(v.key===null?h:v.key)||null,a(d,g,v,S);case Cn:return g=g.get(v.key===null?h:v.key)||null,u(d,g,v,S);case kt:var w=v._init;return m(g,d,h,w(v._payload),S)}if(hr(v)||rr(v))return g=g.get(h)||null,f(d,g,v,S,null);Si(d,v)}return null}function y(g,d,h,v){for(var S=null,w=null,E=d,P=d=0,R=null;E!==null&&P<h.length;P++){E.index>P?(R=E,E=null):R=E.sibling;var V=p(g,E,h[P],v);if(V===null){E===null&&(E=R);break}e&&E&&V.alternate===null&&t(g,E),d=o(V,d,P),w===null?S=V:w.sibling=V,w=V,E=R}if(P===h.length)return n(g,E),W&&qt(g,P),S;if(E===null){for(;P<h.length;P++)E=c(g,h[P],v),E!==null&&(d=o(E,d,P),w===null?S=E:w.sibling=E,w=E);return W&&qt(g,P),S}for(E=r(g,E);P<h.length;P++)R=m(E,g,P,h[P],v),R!==null&&(e&&R.alternate!==null&&E.delete(R.key===null?P:R.key),d=o(R,d,P),w===null?S=R:w.sibling=R,w=R);return e&&E.forEach(function(J){return t(g,J)}),W&&qt(g,P),S}function x(g,d,h,v){var S=rr(h);if(typeof S!="function")throw Error(T(150));if(h=S.call(h),h==null)throw Error(T(151));for(var w=S=null,E=d,P=d=0,R=null,V=h.next();E!==null&&!V.done;P++,V=h.next()){E.index>P?(R=E,E=null):R=E.sibling;var J=p(g,E,V.value,v);if(J===null){E===null&&(E=R);break}e&&E&&J.alternate===null&&t(g,E),d=o(J,d,P),w===null?S=J:w.sibling=J,w=J,E=R}if(V.done)return n(g,E),W&&qt(g,P),S;if(E===null){for(;!V.done;P++,V=h.next())V=c(g,V.value,v),V!==null&&(d=o(V,d,P),w===null?S=V:w.sibling=V,w=V);return W&&qt(g,P),S}for(E=r(g,E);!V.done;P++,V=h.next())V=m(E,g,P,V.value,v),V!==null&&(e&&V.alternate!==null&&E.delete(V.key===null?P:V.key),d=o(V,d,P),w===null?S=V:w.sibling=V,w=V);return e&&E.forEach(function(K){return t(g,K)}),W&&qt(g,P),S}function C(g,d,h,v){if(typeof h=="object"&&h!==null&&h.type===Pn&&h.key===null&&(h=h.props.children),typeof h=="object"&&h!==null){switch(h.$$typeof){case ui:e:{for(var S=h.key,w=d;w!==null;){if(w.key===S){if(S=h.type,S===Pn){if(w.tag===7){n(g,w.sibling),d=i(w,h.props.children),d.return=g,g=d;break e}}else if(w.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===kt&&Ku(S)===w.type){n(g,w.sibling),d=i(w,h.props),d.ref=ar(g,w,h),d.return=g,g=d;break e}n(g,w);break}else t(g,w);w=w.sibling}h.type===Pn?(d=cn(h.props.children,g.mode,v,h.key),d.return=g,g=d):(v=Bi(h.type,h.key,h.props,null,g.mode,v),v.ref=ar(g,d,h),v.return=g,g=v)}return s(g);case Cn:e:{for(w=h.key;d!==null;){if(d.key===w)if(d.tag===4&&d.stateNode.containerInfo===h.containerInfo&&d.stateNode.implementation===h.implementation){n(g,d.sibling),d=i(d,h.children||[]),d.return=g,g=d;break e}else{n(g,d);break}else t(g,d);d=d.sibling}d=vs(h,g.mode,v),d.return=g,g=d}return s(g);case kt:return w=h._init,C(g,d,w(h._payload),v)}if(hr(h))return y(g,d,h,v);if(rr(h))return x(g,d,h,v);Si(g,h)}return typeof h=="string"&&h!==""||typeof h=="number"?(h=""+h,d!==null&&d.tag===6?(n(g,d.sibling),d=i(d,h),d.return=g,g=d):(n(g,d),d=ys(h,g.mode,v),d.return=g,g=d),s(g)):n(g,d)}return C}var Yn=Id(!0),Fd=Id(!1),no=Kt(null),ro=null,Mn=null,ha=null;function ma(){ha=Mn=ro=null}function ga(e){var t=no.current;$(no),e._currentValue=t}function dl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Hn(e,t){ro=e,ha=Mn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ce=!0),e.firstContext=null)}function He(e){var t=e._currentValue;if(ha!==e)if(e={context:e,memoizedValue:t,next:null},Mn===null){if(ro===null)throw Error(T(308));Mn=e,ro.dependencies={lanes:0,firstContext:e}}else Mn=Mn.next=e;return t}var sn=null;function ya(e){sn===null?sn=[e]:sn.push(e)}function zd(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,ya(t)):(n.next=i.next,i.next=n),t.interleaved=n,mt(e,r)}function mt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Ct=!1;function va(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Bd(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function ct(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function jt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,I&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,mt(e,n)}return i=r.interleaved,i===null?(t.next=t,ya(r)):(t.next=i.next,i.next=t),r.interleaved=t,mt(e,n)}function Ni(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ra(e,n)}}function Gu(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=s:o=o.next=s,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function io(e,t,n,r){var i=e.updateQueue;Ct=!1;var o=i.firstBaseUpdate,s=i.lastBaseUpdate,l=i.shared.pending;if(l!==null){i.shared.pending=null;var a=l,u=a.next;a.next=null,s===null?o=u:s.next=u,s=a;var f=e.alternate;f!==null&&(f=f.updateQueue,l=f.lastBaseUpdate,l!==s&&(l===null?f.firstBaseUpdate=u:l.next=u,f.lastBaseUpdate=a))}if(o!==null){var c=i.baseState;s=0,f=u=a=null,l=o;do{var p=l.lane,m=l.eventTime;if((r&p)===p){f!==null&&(f=f.next={eventTime:m,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var y=e,x=l;switch(p=t,m=n,x.tag){case 1:if(y=x.payload,typeof y=="function"){c=y.call(m,c,p);break e}c=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=x.payload,p=typeof y=="function"?y.call(m,c,p):y,p==null)break e;c=X({},c,p);break e;case 2:Ct=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,p=i.effects,p===null?i.effects=[l]:p.push(l))}else m={eventTime:m,lane:p,tag:l.tag,payload:l.payload,callback:l.callback,next:null},f===null?(u=f=m,a=c):f=f.next=m,s|=p;if(l=l.next,l===null){if(l=i.shared.pending,l===null)break;p=l,l=p.next,p.next=null,i.lastBaseUpdate=p,i.shared.pending=null}}while(!0);if(f===null&&(a=c),i.baseState=a,i.firstBaseUpdate=u,i.lastBaseUpdate=f,t=i.shared.interleaved,t!==null){i=t;do s|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);mn|=s,e.lanes=s,e.memoizedState=c}}function Qu(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(T(191,i));i.call(r)}}}var ni={},rt=Kt(ni),Hr=Kt(ni),Wr=Kt(ni);function ln(e){if(e===ni)throw Error(T(174));return e}function xa(e,t){switch(z(Wr,t),z(Hr,e),z(rt,ni),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Gs(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Gs(t,e)}$(rt),z(rt,t)}function Xn(){$(rt),$(Hr),$(Wr)}function Ud(e){ln(Wr.current);var t=ln(rt.current),n=Gs(t,e.type);t!==n&&(z(Hr,e),z(rt,n))}function Sa(e){Hr.current===e&&($(rt),$(Hr))}var G=Kt(0);function oo(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var fs=[];function wa(){for(var e=0;e<fs.length;e++)fs[e]._workInProgressVersionPrimary=null;fs.length=0}var ji=vt.ReactCurrentDispatcher,ds=vt.ReactCurrentBatchConfig,hn=0,Y=null,re=null,se=null,so=!1,Cr=!1,Kr=0,oy=0;function fe(){throw Error(T(321))}function ka(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Je(e[n],t[n]))return!1;return!0}function Ca(e,t,n,r,i,o){if(hn=o,Y=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ji.current=e===null||e.memoizedState===null?uy:cy,e=n(r,i),Cr){o=0;do{if(Cr=!1,Kr=0,25<=o)throw Error(T(301));o+=1,se=re=null,t.updateQueue=null,ji.current=fy,e=n(r,i)}while(Cr)}if(ji.current=lo,t=re!==null&&re.next!==null,hn=0,se=re=Y=null,so=!1,t)throw Error(T(300));return e}function Pa(){var e=Kr!==0;return Kr=0,e}function et(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return se===null?Y.memoizedState=se=e:se=se.next=e,se}function We(){if(re===null){var e=Y.alternate;e=e!==null?e.memoizedState:null}else e=re.next;var t=se===null?Y.memoizedState:se.next;if(t!==null)se=t,re=e;else{if(e===null)throw Error(T(310));re=e,e={memoizedState:re.memoizedState,baseState:re.baseState,baseQueue:re.baseQueue,queue:re.queue,next:null},se===null?Y.memoizedState=se=e:se=se.next=e}return se}function Gr(e,t){return typeof t=="function"?t(e):t}function ps(e){var t=We(),n=t.queue;if(n===null)throw Error(T(311));n.lastRenderedReducer=e;var r=re,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var s=i.next;i.next=o.next,o.next=s}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var l=s=null,a=null,u=o;do{var f=u.lane;if((hn&f)===f)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var c={lane:f,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=c,s=r):a=a.next=c,Y.lanes|=f,mn|=f}u=u.next}while(u!==null&&u!==o);a===null?s=r:a.next=l,Je(r,t.memoizedState)||(Ce=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,Y.lanes|=o,mn|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function hs(e){var t=We(),n=t.queue;if(n===null)throw Error(T(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var s=i=i.next;do o=e(o,s.action),s=s.next;while(s!==i);Je(o,t.memoizedState)||(Ce=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function $d(){}function Hd(e,t){var n=Y,r=We(),i=t(),o=!Je(r.memoizedState,i);if(o&&(r.memoizedState=i,Ce=!0),r=r.queue,Ta(Gd.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||se!==null&&se.memoizedState.tag&1){if(n.flags|=2048,Qr(9,Kd.bind(null,n,r,i,t),void 0,null),le===null)throw Error(T(349));hn&30||Wd(n,t,i)}return i}function Wd(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Y.updateQueue,t===null?(t={lastEffect:null,stores:null},Y.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Kd(e,t,n,r){t.value=n,t.getSnapshot=r,Qd(t)&&Yd(e)}function Gd(e,t,n){return n(function(){Qd(t)&&Yd(e)})}function Qd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Je(e,n)}catch{return!0}}function Yd(e){var t=mt(e,1);t!==null&&Ze(t,e,1,-1)}function Yu(e){var t=et();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Gr,lastRenderedState:e},t.queue=e,e=e.dispatch=ay.bind(null,Y,e),[t.memoizedState,e]}function Qr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Y.updateQueue,t===null?(t={lastEffect:null,stores:null},Y.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Xd(){return We().memoizedState}function Oi(e,t,n,r){var i=et();Y.flags|=e,i.memoizedState=Qr(1|t,n,void 0,r===void 0?null:r)}function Ao(e,t,n,r){var i=We();r=r===void 0?null:r;var o=void 0;if(re!==null){var s=re.memoizedState;if(o=s.destroy,r!==null&&ka(r,s.deps)){i.memoizedState=Qr(t,n,o,r);return}}Y.flags|=e,i.memoizedState=Qr(1|t,n,o,r)}function Xu(e,t){return Oi(8390656,8,e,t)}function Ta(e,t){return Ao(2048,8,e,t)}function Zd(e,t){return Ao(4,2,e,t)}function Jd(e,t){return Ao(4,4,e,t)}function qd(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function bd(e,t,n){return n=n!=null?n.concat([e]):null,Ao(4,4,qd.bind(null,t,e),n)}function Ea(){}function ep(e,t){var n=We();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ka(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function tp(e,t){var n=We();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ka(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function np(e,t,n){return hn&21?(Je(n,t)||(n=ld(),Y.lanes|=n,mn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ce=!0),e.memoizedState=n)}function sy(e,t){var n=F;F=n!==0&&4>n?n:4,e(!0);var r=ds.transition;ds.transition={};try{e(!1),t()}finally{F=n,ds.transition=r}}function rp(){return We().memoizedState}function ly(e,t,n){var r=It(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},ip(e))op(t,n);else if(n=zd(e,t,n,r),n!==null){var i=xe();Ze(n,e,r,i),sp(n,t,r)}}function ay(e,t,n){var r=It(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(ip(e))op(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var s=t.lastRenderedState,l=o(s,n);if(i.hasEagerState=!0,i.eagerState=l,Je(l,s)){var a=t.interleaved;a===null?(i.next=i,ya(t)):(i.next=a.next,a.next=i),t.interleaved=i;return}}catch{}finally{}n=zd(e,t,i,r),n!==null&&(i=xe(),Ze(n,e,r,i),sp(n,t,r))}}function ip(e){var t=e.alternate;return e===Y||t!==null&&t===Y}function op(e,t){Cr=so=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function sp(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ra(e,n)}}var lo={readContext:He,useCallback:fe,useContext:fe,useEffect:fe,useImperativeHandle:fe,useInsertionEffect:fe,useLayoutEffect:fe,useMemo:fe,useReducer:fe,useRef:fe,useState:fe,useDebugValue:fe,useDeferredValue:fe,useTransition:fe,useMutableSource:fe,useSyncExternalStore:fe,useId:fe,unstable_isNewReconciler:!1},uy={readContext:He,useCallback:function(e,t){return et().memoizedState=[e,t===void 0?null:t],e},useContext:He,useEffect:Xu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Oi(4194308,4,qd.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Oi(4194308,4,e,t)},useInsertionEffect:function(e,t){return Oi(4,2,e,t)},useMemo:function(e,t){var n=et();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=et();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=ly.bind(null,Y,e),[r.memoizedState,e]},useRef:function(e){var t=et();return e={current:e},t.memoizedState=e},useState:Yu,useDebugValue:Ea,useDeferredValue:function(e){return et().memoizedState=e},useTransition:function(){var e=Yu(!1),t=e[0];return e=sy.bind(null,e[1]),et().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Y,i=et();if(W){if(n===void 0)throw Error(T(407));n=n()}else{if(n=t(),le===null)throw Error(T(349));hn&30||Wd(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,Xu(Gd.bind(null,r,o,e),[e]),r.flags|=2048,Qr(9,Kd.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=et(),t=le.identifierPrefix;if(W){var n=at,r=lt;n=(r&~(1<<32-Xe(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Kr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=oy++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},cy={readContext:He,useCallback:ep,useContext:He,useEffect:Ta,useImperativeHandle:bd,useInsertionEffect:Zd,useLayoutEffect:Jd,useMemo:tp,useReducer:ps,useRef:Xd,useState:function(){return ps(Gr)},useDebugValue:Ea,useDeferredValue:function(e){var t=We();return np(t,re.memoizedState,e)},useTransition:function(){var e=ps(Gr)[0],t=We().memoizedState;return[e,t]},useMutableSource:$d,useSyncExternalStore:Hd,useId:rp,unstable_isNewReconciler:!1},fy={readContext:He,useCallback:ep,useContext:He,useEffect:Ta,useImperativeHandle:bd,useInsertionEffect:Zd,useLayoutEffect:Jd,useMemo:tp,useReducer:hs,useRef:Xd,useState:function(){return hs(Gr)},useDebugValue:Ea,useDeferredValue:function(e){var t=We();return re===null?t.memoizedState=e:np(t,re.memoizedState,e)},useTransition:function(){var e=hs(Gr)[0],t=We().memoizedState;return[e,t]},useMutableSource:$d,useSyncExternalStore:Hd,useId:rp,unstable_isNewReconciler:!1};function Ge(e,t){if(e&&e.defaultProps){t=X({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function pl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:X({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Ro={isMounted:function(e){return(e=e._reactInternals)?vn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=xe(),i=It(e),o=ct(r,i);o.payload=t,n!=null&&(o.callback=n),t=jt(e,o,i),t!==null&&(Ze(t,e,i,r),Ni(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=xe(),i=It(e),o=ct(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=jt(e,o,i),t!==null&&(Ze(t,e,i,r),Ni(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=xe(),r=It(e),i=ct(n,r);i.tag=2,t!=null&&(i.callback=t),t=jt(e,i,r),t!==null&&(Ze(t,e,r,n),Ni(t,e,r))}};function Zu(e,t,n,r,i,o,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,s):t.prototype&&t.prototype.isPureReactComponent?!zr(n,r)||!zr(i,o):!0}function lp(e,t,n){var r=!1,i=Ut,o=t.contextType;return typeof o=="object"&&o!==null?o=He(o):(i=Te(t)?dn:me.current,r=t.contextTypes,o=(r=r!=null)?Gn(e,i):Ut),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ro,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function Ju(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Ro.enqueueReplaceState(t,t.state,null)}function hl(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},va(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=He(o):(o=Te(t)?dn:me.current,i.context=Gn(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(pl(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&Ro.enqueueReplaceState(i,i.state,null),io(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function Zn(e,t){try{var n="",r=t;do n+=zm(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function ms(e,t,n){return{value:e,source:null,stack:n!=null?n:null,digest:t!=null?t:null}}function ml(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var dy=typeof WeakMap=="function"?WeakMap:Map;function ap(e,t,n){n=ct(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){uo||(uo=!0,Tl=r),ml(e,t)},n}function up(e,t,n){n=ct(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){ml(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){ml(e,t),typeof r!="function"&&(Ot===null?Ot=new Set([this]):Ot.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function qu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new dy;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=Ey.bind(null,e,t,n),t.then(e,e))}function bu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function ec(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=ct(-1,1),t.tag=2,jt(n,t,1))),n.lanes|=1),e)}var py=vt.ReactCurrentOwner,Ce=!1;function ve(e,t,n,r){t.child=e===null?Fd(t,null,n,r):Yn(t,e.child,n,r)}function tc(e,t,n,r,i){n=n.render;var o=t.ref;return Hn(t,i),r=Ca(e,t,n,r,o,i),n=Pa(),e!==null&&!Ce?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,gt(e,t,i)):(W&&n&&fa(t),t.flags|=1,ve(e,t,r,i),t.child)}function nc(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!Na(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,cp(e,t,o,r,i)):(e=Bi(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var s=o.memoizedProps;if(n=n.compare,n=n!==null?n:zr,n(s,r)&&e.ref===t.ref)return gt(e,t,i)}return t.flags|=1,e=Ft(o,r),e.ref=t.ref,e.return=t,t.child=e}function cp(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(zr(o,r)&&e.ref===t.ref)if(Ce=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(Ce=!0);else return t.lanes=e.lanes,gt(e,t,i)}return gl(e,t,n,r,i)}function fp(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},z(Nn,Ae),Ae|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,z(Nn,Ae),Ae|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,z(Nn,Ae),Ae|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,z(Nn,Ae),Ae|=r;return ve(e,t,i,n),t.child}function dp(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function gl(e,t,n,r,i){var o=Te(n)?dn:me.current;return o=Gn(t,o),Hn(t,i),n=Ca(e,t,n,r,o,i),r=Pa(),e!==null&&!Ce?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,gt(e,t,i)):(W&&r&&fa(t),t.flags|=1,ve(e,t,n,i),t.child)}function rc(e,t,n,r,i){if(Te(n)){var o=!0;bi(t)}else o=!1;if(Hn(t,i),t.stateNode===null)Ii(e,t),lp(t,n,r),hl(t,n,r,i),r=!0;else if(e===null){var s=t.stateNode,l=t.memoizedProps;s.props=l;var a=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=He(u):(u=Te(n)?dn:me.current,u=Gn(t,u));var f=n.getDerivedStateFromProps,c=typeof f=="function"||typeof s.getSnapshotBeforeUpdate=="function";c||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==r||a!==u)&&Ju(t,s,r,u),Ct=!1;var p=t.memoizedState;s.state=p,io(t,r,s,i),a=t.memoizedState,l!==r||p!==a||Pe.current||Ct?(typeof f=="function"&&(pl(t,n,f,r),a=t.memoizedState),(l=Ct||Zu(t,n,l,r,p,a,u))?(c||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),s.props=r,s.state=a,s.context=u,r=l):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,Bd(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:Ge(t.type,l),s.props=u,c=t.pendingProps,p=s.context,a=n.contextType,typeof a=="object"&&a!==null?a=He(a):(a=Te(n)?dn:me.current,a=Gn(t,a));var m=n.getDerivedStateFromProps;(f=typeof m=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==c||p!==a)&&Ju(t,s,r,a),Ct=!1,p=t.memoizedState,s.state=p,io(t,r,s,i);var y=t.memoizedState;l!==c||p!==y||Pe.current||Ct?(typeof m=="function"&&(pl(t,n,m,r),y=t.memoizedState),(u=Ct||Zu(t,n,u,r,p,y,a)||!1)?(f||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,y,a),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,y,a)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=y),s.props=r,s.state=y,s.context=a,r=u):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return yl(e,t,n,r,o,i)}function yl(e,t,n,r,i,o){dp(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return i&&$u(t,n,!1),gt(e,t,o);r=t.stateNode,py.current=t;var l=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=Yn(t,e.child,null,o),t.child=Yn(t,null,l,o)):ve(e,t,l,o),t.memoizedState=r.state,i&&$u(t,n,!0),t.child}function pp(e){var t=e.stateNode;t.pendingContext?Uu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Uu(e,t.context,!1),xa(e,t.containerInfo)}function ic(e,t,n,r,i){return Qn(),pa(i),t.flags|=256,ve(e,t,n,r),t.child}var vl={dehydrated:null,treeContext:null,retryLane:0};function xl(e){return{baseLanes:e,cachePool:null,transitions:null}}function hp(e,t,n){var r=t.pendingProps,i=G.current,o=!1,s=(t.flags&128)!==0,l;if((l=s)||(l=e!==null&&e.memoizedState===null?!1:(i&2)!==0),l?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),z(G,i&1),e===null)return fl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,o?(r=t.mode,o=t.child,s={mode:"hidden",children:s},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=s):o=_o(s,r,0,null),e=cn(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=xl(n),t.memoizedState=vl,e):Da(t,s));if(i=e.memoizedState,i!==null&&(l=i.dehydrated,l!==null))return hy(e,t,s,r,l,i,n);if(o){o=r.fallback,s=t.mode,i=e.child,l=i.sibling;var a={mode:"hidden",children:r.children};return!(s&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Ft(i,a),r.subtreeFlags=i.subtreeFlags&14680064),l!==null?o=Ft(l,o):(o=cn(o,s,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,s=e.child.memoizedState,s=s===null?xl(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~n,t.memoizedState=vl,r}return o=e.child,e=o.sibling,r=Ft(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Da(e,t){return t=_o({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function wi(e,t,n,r){return r!==null&&pa(r),Yn(t,e.child,null,n),e=Da(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function hy(e,t,n,r,i,o,s){if(n)return t.flags&256?(t.flags&=-257,r=ms(Error(T(422))),wi(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=_o({mode:"visible",children:r.children},i,0,null),o=cn(o,i,s,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&Yn(t,e.child,null,s),t.child.memoizedState=xl(s),t.memoizedState=vl,o);if(!(t.mode&1))return wi(e,t,s,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var l=r.dgst;return r=l,o=Error(T(419)),r=ms(o,r,void 0),wi(e,t,s,r)}if(l=(s&e.childLanes)!==0,Ce||l){if(r=le,r!==null){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|s)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,mt(e,i),Ze(r,e,i,-1))}return _a(),r=ms(Error(T(421))),wi(e,t,s,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=Dy.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,Re=Nt(i.nextSibling),Ve=t,W=!0,Ye=null,e!==null&&(ze[Be++]=lt,ze[Be++]=at,ze[Be++]=pn,lt=e.id,at=e.overflow,pn=t),t=Da(t,r.children),t.flags|=4096,t)}function oc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),dl(e.return,t,n)}function gs(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function mp(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(ve(e,t,r.children,n),r=G.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&oc(e,n,t);else if(e.tag===19)oc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(z(G,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&oo(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),gs(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&oo(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}gs(t,!0,n,null,o);break;case"together":gs(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ii(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function gt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),mn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(T(153));if(t.child!==null){for(e=t.child,n=Ft(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Ft(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function my(e,t,n){switch(t.tag){case 3:pp(t),Qn();break;case 5:Ud(t);break;case 1:Te(t.type)&&bi(t);break;case 4:xa(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;z(no,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(z(G,G.current&1),t.flags|=128,null):n&t.child.childLanes?hp(e,t,n):(z(G,G.current&1),e=gt(e,t,n),e!==null?e.sibling:null);z(G,G.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return mp(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),z(G,G.current),r)break;return null;case 22:case 23:return t.lanes=0,fp(e,t,n)}return gt(e,t,n)}var gp,Sl,yp,vp;gp=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Sl=function(){};yp=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,ln(rt.current);var o=null;switch(n){case"input":i=$s(e,i),r=$s(e,r),o=[];break;case"select":i=X({},i,{value:void 0}),r=X({},r,{value:void 0}),o=[];break;case"textarea":i=Ks(e,i),r=Ks(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Ji)}Qs(n,r);var s;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var l=i[u];for(s in l)l.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Mr.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in r){var a=r[u];if(l=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(s in l)!l.hasOwnProperty(s)||a&&a.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in a)a.hasOwnProperty(s)&&l[s]!==a[s]&&(n||(n={}),n[s]=a[s])}else n||(o||(o=[]),o.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(o=o||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(o=o||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Mr.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&U("scroll",e),o||l===a||(o=[])):(o=o||[]).push(u,a))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}};vp=function(e,t,n,r){n!==r&&(t.flags|=4)};function ur(e,t){if(!W)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function de(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function gy(e,t,n){var r=t.pendingProps;switch(da(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return de(t),null;case 1:return Te(t.type)&&qi(),de(t),null;case 3:return r=t.stateNode,Xn(),$(Pe),$(me),wa(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(xi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ye!==null&&(Ll(Ye),Ye=null))),Sl(e,t),de(t),null;case 5:Sa(t);var i=ln(Wr.current);if(n=t.type,e!==null&&t.stateNode!=null)yp(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(T(166));return de(t),null}if(e=ln(rt.current),xi(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[tt]=t,r[$r]=o,e=(t.mode&1)!==0,n){case"dialog":U("cancel",r),U("close",r);break;case"iframe":case"object":case"embed":U("load",r);break;case"video":case"audio":for(i=0;i<gr.length;i++)U(gr[i],r);break;case"source":U("error",r);break;case"img":case"image":case"link":U("error",r),U("load",r);break;case"details":U("toggle",r);break;case"input":hu(r,o),U("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},U("invalid",r);break;case"textarea":gu(r,o),U("invalid",r)}Qs(n,o),i=null;for(var s in o)if(o.hasOwnProperty(s)){var l=o[s];s==="children"?typeof l=="string"?r.textContent!==l&&(o.suppressHydrationWarning!==!0&&vi(r.textContent,l,e),i=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(o.suppressHydrationWarning!==!0&&vi(r.textContent,l,e),i=["children",""+l]):Mr.hasOwnProperty(s)&&l!=null&&s==="onScroll"&&U("scroll",r)}switch(n){case"input":ci(r),mu(r,o,!0);break;case"textarea":ci(r),yu(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=Ji)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Gf(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[tt]=t,e[$r]=r,gp(e,t,!1,!1),t.stateNode=e;e:{switch(s=Ys(n,r),n){case"dialog":U("cancel",e),U("close",e),i=r;break;case"iframe":case"object":case"embed":U("load",e),i=r;break;case"video":case"audio":for(i=0;i<gr.length;i++)U(gr[i],e);i=r;break;case"source":U("error",e),i=r;break;case"img":case"image":case"link":U("error",e),U("load",e),i=r;break;case"details":U("toggle",e),i=r;break;case"input":hu(e,r),i=$s(e,r),U("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=X({},r,{value:void 0}),U("invalid",e);break;case"textarea":gu(e,r),i=Ks(e,r),U("invalid",e);break;default:i=r}Qs(n,i),l=i;for(o in l)if(l.hasOwnProperty(o)){var a=l[o];o==="style"?Xf(e,a):o==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&Qf(e,a)):o==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&_r(e,a):typeof a=="number"&&_r(e,""+a):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(Mr.hasOwnProperty(o)?a!=null&&o==="onScroll"&&U("scroll",e):a!=null&&Jl(e,o,a,s))}switch(n){case"input":ci(e),mu(e,r,!1);break;case"textarea":ci(e),yu(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Bt(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?zn(e,!!r.multiple,o,!1):r.defaultValue!=null&&zn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=Ji)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return de(t),null;case 6:if(e&&t.stateNode!=null)vp(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(T(166));if(n=ln(Wr.current),ln(rt.current),xi(t)){if(r=t.stateNode,n=t.memoizedProps,r[tt]=t,(o=r.nodeValue!==n)&&(e=Ve,e!==null))switch(e.tag){case 3:vi(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&vi(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[tt]=t,t.stateNode=r}return de(t),null;case 13:if($(G),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(W&&Re!==null&&t.mode&1&&!(t.flags&128))Od(),Qn(),t.flags|=98560,o=!1;else if(o=xi(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(T(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(T(317));o[tt]=t}else Qn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;de(t),o=!1}else Ye!==null&&(Ll(Ye),Ye=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||G.current&1?ie===0&&(ie=3):_a())),t.updateQueue!==null&&(t.flags|=4),de(t),null);case 4:return Xn(),Sl(e,t),e===null&&Br(t.stateNode.containerInfo),de(t),null;case 10:return ga(t.type._context),de(t),null;case 17:return Te(t.type)&&qi(),de(t),null;case 19:if($(G),o=t.memoizedState,o===null)return de(t),null;if(r=(t.flags&128)!==0,s=o.rendering,s===null)if(r)ur(o,!1);else{if(ie!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=oo(e),s!==null){for(t.flags|=128,ur(o,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,s=o.alternate,s===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return z(G,G.current&1|2),t.child}e=e.sibling}o.tail!==null&&b()>Jn&&(t.flags|=128,r=!0,ur(o,!1),t.lanes=4194304)}else{if(!r)if(e=oo(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),ur(o,!0),o.tail===null&&o.tailMode==="hidden"&&!s.alternate&&!W)return de(t),null}else 2*b()-o.renderingStartTime>Jn&&n!==1073741824&&(t.flags|=128,r=!0,ur(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(n=o.last,n!==null?n.sibling=s:t.child=s,o.last=s)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=b(),t.sibling=null,n=G.current,z(G,r?n&1|2:n&1),t):(de(t),null);case 22:case 23:return Ma(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ae&1073741824&&(de(t),t.subtreeFlags&6&&(t.flags|=8192)):de(t),null;case 24:return null;case 25:return null}throw Error(T(156,t.tag))}function yy(e,t){switch(da(t),t.tag){case 1:return Te(t.type)&&qi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Xn(),$(Pe),$(me),wa(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Sa(t),null;case 13:if($(G),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(T(340));Qn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return $(G),null;case 4:return Xn(),null;case 10:return ga(t.type._context),null;case 22:case 23:return Ma(),null;case 24:return null;default:return null}}var ki=!1,he=!1,vy=typeof WeakSet=="function"?WeakSet:Set,A=null;function _n(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Z(e,t,r)}else n.current=null}function wl(e,t,n){try{n()}catch(r){Z(e,t,r)}}var sc=!1;function xy(e,t){if(il=Yi,e=Cd(),ca(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var s=0,l=-1,a=-1,u=0,f=0,c=e,p=null;t:for(;;){for(var m;c!==n||i!==0&&c.nodeType!==3||(l=s+i),c!==o||r!==0&&c.nodeType!==3||(a=s+r),c.nodeType===3&&(s+=c.nodeValue.length),(m=c.firstChild)!==null;)p=c,c=m;for(;;){if(c===e)break t;if(p===n&&++u===i&&(l=s),p===o&&++f===r&&(a=s),(m=c.nextSibling)!==null)break;c=p,p=c.parentNode}c=m}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(ol={focusedElem:e,selectionRange:n},Yi=!1,A=t;A!==null;)if(t=A,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,A=e;else for(;A!==null;){t=A;try{var y=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var x=y.memoizedProps,C=y.memoizedState,g=t.stateNode,d=g.getSnapshotBeforeUpdate(t.elementType===t.type?x:Ge(t.type,x),C);g.__reactInternalSnapshotBeforeUpdate=d}break;case 3:var h=t.stateNode.containerInfo;h.nodeType===1?h.textContent="":h.nodeType===9&&h.documentElement&&h.removeChild(h.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(T(163))}}catch(v){Z(t,t.return,v)}if(e=t.sibling,e!==null){e.return=t.return,A=e;break}A=t.return}return y=sc,sc=!1,y}function Pr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&wl(t,n,o)}i=i.next}while(i!==r)}}function Vo(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function kl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function xp(e){var t=e.alternate;t!==null&&(e.alternate=null,xp(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[tt],delete t[$r],delete t[al],delete t[ty],delete t[ny])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Sp(e){return e.tag===5||e.tag===3||e.tag===4}function lc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Sp(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Cl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Ji));else if(r!==4&&(e=e.child,e!==null))for(Cl(e,t,n),e=e.sibling;e!==null;)Cl(e,t,n),e=e.sibling}function Pl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Pl(e,t,n),e=e.sibling;e!==null;)Pl(e,t,n),e=e.sibling}var ae=null,Qe=!1;function St(e,t,n){for(n=n.child;n!==null;)wp(e,t,n),n=n.sibling}function wp(e,t,n){if(nt&&typeof nt.onCommitFiberUnmount=="function")try{nt.onCommitFiberUnmount(Co,n)}catch{}switch(n.tag){case 5:he||_n(n,t);case 6:var r=ae,i=Qe;ae=null,St(e,t,n),ae=r,Qe=i,ae!==null&&(Qe?(e=ae,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ae.removeChild(n.stateNode));break;case 18:ae!==null&&(Qe?(e=ae,n=n.stateNode,e.nodeType===8?us(e.parentNode,n):e.nodeType===1&&us(e,n),Ir(e)):us(ae,n.stateNode));break;case 4:r=ae,i=Qe,ae=n.stateNode.containerInfo,Qe=!0,St(e,t,n),ae=r,Qe=i;break;case 0:case 11:case 14:case 15:if(!he&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,s=o.destroy;o=o.tag,s!==void 0&&(o&2||o&4)&&wl(n,t,s),i=i.next}while(i!==r)}St(e,t,n);break;case 1:if(!he&&(_n(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){Z(n,t,l)}St(e,t,n);break;case 21:St(e,t,n);break;case 22:n.mode&1?(he=(r=he)||n.memoizedState!==null,St(e,t,n),he=r):St(e,t,n);break;default:St(e,t,n)}}function ac(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new vy),t.forEach(function(r){var i=Ly.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function Ke(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,s=t,l=s;e:for(;l!==null;){switch(l.tag){case 5:ae=l.stateNode,Qe=!1;break e;case 3:ae=l.stateNode.containerInfo,Qe=!0;break e;case 4:ae=l.stateNode.containerInfo,Qe=!0;break e}l=l.return}if(ae===null)throw Error(T(160));wp(o,s,i),ae=null,Qe=!1;var a=i.alternate;a!==null&&(a.return=null),i.return=null}catch(u){Z(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)kp(t,e),t=t.sibling}function kp(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ke(t,e),be(e),r&4){try{Pr(3,e,e.return),Vo(3,e)}catch(x){Z(e,e.return,x)}try{Pr(5,e,e.return)}catch(x){Z(e,e.return,x)}}break;case 1:Ke(t,e),be(e),r&512&&n!==null&&_n(n,n.return);break;case 5:if(Ke(t,e),be(e),r&512&&n!==null&&_n(n,n.return),e.flags&32){var i=e.stateNode;try{_r(i,"")}catch(x){Z(e,e.return,x)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,s=n!==null?n.memoizedProps:o,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&o.type==="radio"&&o.name!=null&&Wf(i,o),Ys(l,s);var u=Ys(l,o);for(s=0;s<a.length;s+=2){var f=a[s],c=a[s+1];f==="style"?Xf(i,c):f==="dangerouslySetInnerHTML"?Qf(i,c):f==="children"?_r(i,c):Jl(i,f,c,u)}switch(l){case"input":Hs(i,o);break;case"textarea":Kf(i,o);break;case"select":var p=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var m=o.value;m!=null?zn(i,!!o.multiple,m,!1):p!==!!o.multiple&&(o.defaultValue!=null?zn(i,!!o.multiple,o.defaultValue,!0):zn(i,!!o.multiple,o.multiple?[]:"",!1))}i[$r]=o}catch(x){Z(e,e.return,x)}}break;case 6:if(Ke(t,e),be(e),r&4){if(e.stateNode===null)throw Error(T(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(x){Z(e,e.return,x)}}break;case 3:if(Ke(t,e),be(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Ir(t.containerInfo)}catch(x){Z(e,e.return,x)}break;case 4:Ke(t,e),be(e);break;case 13:Ke(t,e),be(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(Ra=b())),r&4&&ac(e);break;case 22:if(f=n!==null&&n.memoizedState!==null,e.mode&1?(he=(u=he)||f,Ke(t,e),he=u):Ke(t,e),be(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!f&&e.mode&1)for(A=e,f=e.child;f!==null;){for(c=A=f;A!==null;){switch(p=A,m=p.child,p.tag){case 0:case 11:case 14:case 15:Pr(4,p,p.return);break;case 1:_n(p,p.return);var y=p.stateNode;if(typeof y.componentWillUnmount=="function"){r=p,n=p.return;try{t=r,y.props=t.memoizedProps,y.state=t.memoizedState,y.componentWillUnmount()}catch(x){Z(r,n,x)}}break;case 5:_n(p,p.return);break;case 22:if(p.memoizedState!==null){cc(c);continue}}m!==null?(m.return=p,A=m):cc(c)}f=f.sibling}e:for(f=null,c=e;;){if(c.tag===5){if(f===null){f=c;try{i=c.stateNode,u?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(l=c.stateNode,a=c.memoizedProps.style,s=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=Yf("display",s))}catch(x){Z(e,e.return,x)}}}else if(c.tag===6){if(f===null)try{c.stateNode.nodeValue=u?"":c.memoizedProps}catch(x){Z(e,e.return,x)}}else if((c.tag!==22&&c.tag!==23||c.memoizedState===null||c===e)&&c.child!==null){c.child.return=c,c=c.child;continue}if(c===e)break e;for(;c.sibling===null;){if(c.return===null||c.return===e)break e;f===c&&(f=null),c=c.return}f===c&&(f=null),c.sibling.return=c.return,c=c.sibling}}break;case 19:Ke(t,e),be(e),r&4&&ac(e);break;case 21:break;default:Ke(t,e),be(e)}}function be(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Sp(n)){var r=n;break e}n=n.return}throw Error(T(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(_r(i,""),r.flags&=-33);var o=lc(e);Pl(e,o,i);break;case 3:case 4:var s=r.stateNode.containerInfo,l=lc(e);Cl(e,l,s);break;default:throw Error(T(161))}}catch(a){Z(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Sy(e,t,n){A=e,Cp(e)}function Cp(e,t,n){for(var r=(e.mode&1)!==0;A!==null;){var i=A,o=i.child;if(i.tag===22&&r){var s=i.memoizedState!==null||ki;if(!s){var l=i.alternate,a=l!==null&&l.memoizedState!==null||he;l=ki;var u=he;if(ki=s,(he=a)&&!u)for(A=i;A!==null;)s=A,a=s.child,s.tag===22&&s.memoizedState!==null?fc(i):a!==null?(a.return=s,A=a):fc(i);for(;o!==null;)A=o,Cp(o),o=o.sibling;A=i,ki=l,he=u}uc(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,A=o):uc(e)}}function uc(e){for(;A!==null;){var t=A;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:he||Vo(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!he)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:Ge(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&Qu(t,o,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Qu(t,s,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var f=u.memoizedState;if(f!==null){var c=f.dehydrated;c!==null&&Ir(c)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(T(163))}he||t.flags&512&&kl(t)}catch(p){Z(t,t.return,p)}}if(t===e){A=null;break}if(n=t.sibling,n!==null){n.return=t.return,A=n;break}A=t.return}}function cc(e){for(;A!==null;){var t=A;if(t===e){A=null;break}var n=t.sibling;if(n!==null){n.return=t.return,A=n;break}A=t.return}}function fc(e){for(;A!==null;){var t=A;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Vo(4,t)}catch(a){Z(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(a){Z(t,i,a)}}var o=t.return;try{kl(t)}catch(a){Z(t,o,a)}break;case 5:var s=t.return;try{kl(t)}catch(a){Z(t,s,a)}}}catch(a){Z(t,t.return,a)}if(t===e){A=null;break}var l=t.sibling;if(l!==null){l.return=t.return,A=l;break}A=t.return}}var wy=Math.ceil,ao=vt.ReactCurrentDispatcher,La=vt.ReactCurrentOwner,$e=vt.ReactCurrentBatchConfig,I=0,le=null,ne=null,ue=0,Ae=0,Nn=Kt(0),ie=0,Yr=null,mn=0,Mo=0,Aa=0,Tr=null,ke=null,Ra=0,Jn=1/0,ot=null,uo=!1,Tl=null,Ot=null,Ci=!1,Lt=null,co=0,Er=0,El=null,Fi=-1,zi=0;function xe(){return I&6?b():Fi!==-1?Fi:Fi=b()}function It(e){return e.mode&1?I&2&&ue!==0?ue&-ue:iy.transition!==null?(zi===0&&(zi=ld()),zi):(e=F,e!==0||(e=window.event,e=e===void 0?16:hd(e.type)),e):1}function Ze(e,t,n,r){if(50<Er)throw Er=0,El=null,Error(T(185));br(e,n,r),(!(I&2)||e!==le)&&(e===le&&(!(I&2)&&(Mo|=n),ie===4&&Et(e,ue)),Ee(e,r),n===1&&I===0&&!(t.mode&1)&&(Jn=b()+500,Lo&&Gt()))}function Ee(e,t){var n=e.callbackNode;ig(e,t);var r=Qi(e,e===le?ue:0);if(r===0)n!==null&&Su(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Su(n),t===1)e.tag===0?ry(dc.bind(null,e)):_d(dc.bind(null,e)),bg(function(){!(I&6)&&Gt()}),n=null;else{switch(ad(r)){case 1:n=na;break;case 4:n=od;break;case 16:n=Gi;break;case 536870912:n=sd;break;default:n=Gi}n=Vp(n,Pp.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Pp(e,t){if(Fi=-1,zi=0,I&6)throw Error(T(327));var n=e.callbackNode;if(Wn()&&e.callbackNode!==n)return null;var r=Qi(e,e===le?ue:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=fo(e,r);else{t=r;var i=I;I|=2;var o=Ep();(le!==e||ue!==t)&&(ot=null,Jn=b()+500,un(e,t));do try{Py();break}catch(l){Tp(e,l)}while(!0);ma(),ao.current=o,I=i,ne!==null?t=0:(le=null,ue=0,t=ie)}if(t!==0){if(t===2&&(i=bs(e),i!==0&&(r=i,t=Dl(e,i))),t===1)throw n=Yr,un(e,0),Et(e,r),Ee(e,b()),n;if(t===6)Et(e,r);else{if(i=e.current.alternate,!(r&30)&&!ky(i)&&(t=fo(e,r),t===2&&(o=bs(e),o!==0&&(r=o,t=Dl(e,o))),t===1))throw n=Yr,un(e,0),Et(e,r),Ee(e,b()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(T(345));case 2:bt(e,ke,ot);break;case 3:if(Et(e,r),(r&130023424)===r&&(t=Ra+500-b(),10<t)){if(Qi(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){xe(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=ll(bt.bind(null,e,ke,ot),t);break}bt(e,ke,ot);break;case 4:if(Et(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-Xe(r);o=1<<s,s=t[s],s>i&&(i=s),r&=~o}if(r=i,r=b()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*wy(r/1960))-r,10<r){e.timeoutHandle=ll(bt.bind(null,e,ke,ot),r);break}bt(e,ke,ot);break;case 5:bt(e,ke,ot);break;default:throw Error(T(329))}}}return Ee(e,b()),e.callbackNode===n?Pp.bind(null,e):null}function Dl(e,t){var n=Tr;return e.current.memoizedState.isDehydrated&&(un(e,t).flags|=256),e=fo(e,t),e!==2&&(t=ke,ke=n,t!==null&&Ll(t)),e}function Ll(e){ke===null?ke=e:ke.push.apply(ke,e)}function ky(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!Je(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Et(e,t){for(t&=~Aa,t&=~Mo,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Xe(t),r=1<<n;e[n]=-1,t&=~r}}function dc(e){if(I&6)throw Error(T(327));Wn();var t=Qi(e,0);if(!(t&1))return Ee(e,b()),null;var n=fo(e,t);if(e.tag!==0&&n===2){var r=bs(e);r!==0&&(t=r,n=Dl(e,r))}if(n===1)throw n=Yr,un(e,0),Et(e,t),Ee(e,b()),n;if(n===6)throw Error(T(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,bt(e,ke,ot),Ee(e,b()),null}function Va(e,t){var n=I;I|=1;try{return e(t)}finally{I=n,I===0&&(Jn=b()+500,Lo&&Gt())}}function gn(e){Lt!==null&&Lt.tag===0&&!(I&6)&&Wn();var t=I;I|=1;var n=$e.transition,r=F;try{if($e.transition=null,F=1,e)return e()}finally{F=r,$e.transition=n,I=t,!(I&6)&&Gt()}}function Ma(){Ae=Nn.current,$(Nn)}function un(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,qg(n)),ne!==null)for(n=ne.return;n!==null;){var r=n;switch(da(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&qi();break;case 3:Xn(),$(Pe),$(me),wa();break;case 5:Sa(r);break;case 4:Xn();break;case 13:$(G);break;case 19:$(G);break;case 10:ga(r.type._context);break;case 22:case 23:Ma()}n=n.return}if(le=e,ne=e=Ft(e.current,null),ue=Ae=t,ie=0,Yr=null,Aa=Mo=mn=0,ke=Tr=null,sn!==null){for(t=0;t<sn.length;t++)if(n=sn[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var s=o.next;o.next=i,r.next=s}n.pending=r}sn=null}return e}function Tp(e,t){do{var n=ne;try{if(ma(),ji.current=lo,so){for(var r=Y.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}so=!1}if(hn=0,se=re=Y=null,Cr=!1,Kr=0,La.current=null,n===null||n.return===null){ie=1,Yr=t,ne=null;break}e:{var o=e,s=n.return,l=n,a=t;if(t=ue,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,f=l,c=f.tag;if(!(f.mode&1)&&(c===0||c===11||c===15)){var p=f.alternate;p?(f.updateQueue=p.updateQueue,f.memoizedState=p.memoizedState,f.lanes=p.lanes):(f.updateQueue=null,f.memoizedState=null)}var m=bu(s);if(m!==null){m.flags&=-257,ec(m,s,l,o,t),m.mode&1&&qu(o,u,t),t=m,a=u;var y=t.updateQueue;if(y===null){var x=new Set;x.add(a),t.updateQueue=x}else y.add(a);break e}else{if(!(t&1)){qu(o,u,t),_a();break e}a=Error(T(426))}}else if(W&&l.mode&1){var C=bu(s);if(C!==null){!(C.flags&65536)&&(C.flags|=256),ec(C,s,l,o,t),pa(Zn(a,l));break e}}o=a=Zn(a,l),ie!==4&&(ie=2),Tr===null?Tr=[o]:Tr.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var g=ap(o,a,t);Gu(o,g);break e;case 1:l=a;var d=o.type,h=o.stateNode;if(!(o.flags&128)&&(typeof d.getDerivedStateFromError=="function"||h!==null&&typeof h.componentDidCatch=="function"&&(Ot===null||!Ot.has(h)))){o.flags|=65536,t&=-t,o.lanes|=t;var v=up(o,l,t);Gu(o,v);break e}}o=o.return}while(o!==null)}Lp(n)}catch(S){t=S,ne===n&&n!==null&&(ne=n=n.return);continue}break}while(!0)}function Ep(){var e=ao.current;return ao.current=lo,e===null?lo:e}function _a(){(ie===0||ie===3||ie===2)&&(ie=4),le===null||!(mn&268435455)&&!(Mo&268435455)||Et(le,ue)}function fo(e,t){var n=I;I|=2;var r=Ep();(le!==e||ue!==t)&&(ot=null,un(e,t));do try{Cy();break}catch(i){Tp(e,i)}while(!0);if(ma(),I=n,ao.current=r,ne!==null)throw Error(T(261));return le=null,ue=0,ie}function Cy(){for(;ne!==null;)Dp(ne)}function Py(){for(;ne!==null&&!Xm();)Dp(ne)}function Dp(e){var t=Rp(e.alternate,e,Ae);e.memoizedProps=e.pendingProps,t===null?Lp(e):ne=t,La.current=null}function Lp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=yy(n,t),n!==null){n.flags&=32767,ne=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ie=6,ne=null;return}}else if(n=gy(n,t,Ae),n!==null){ne=n;return}if(t=t.sibling,t!==null){ne=t;return}ne=t=e}while(t!==null);ie===0&&(ie=5)}function bt(e,t,n){var r=F,i=$e.transition;try{$e.transition=null,F=1,Ty(e,t,n,r)}finally{$e.transition=i,F=r}return null}function Ty(e,t,n,r){do Wn();while(Lt!==null);if(I&6)throw Error(T(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(T(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(og(e,o),e===le&&(ne=le=null,ue=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Ci||(Ci=!0,Vp(Gi,function(){return Wn(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=$e.transition,$e.transition=null;var s=F;F=1;var l=I;I|=4,La.current=null,xy(e,n),kp(n,e),Kg(ol),Yi=!!il,ol=il=null,e.current=n,Sy(n),Zm(),I=l,F=s,$e.transition=o}else e.current=n;if(Ci&&(Ci=!1,Lt=e,co=i),o=e.pendingLanes,o===0&&(Ot=null),bm(n.stateNode),Ee(e,b()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(uo)throw uo=!1,e=Tl,Tl=null,e;return co&1&&e.tag!==0&&Wn(),o=e.pendingLanes,o&1?e===El?Er++:(Er=0,El=e):Er=0,Gt(),null}function Wn(){if(Lt!==null){var e=ad(co),t=$e.transition,n=F;try{if($e.transition=null,F=16>e?16:e,Lt===null)var r=!1;else{if(e=Lt,Lt=null,co=0,I&6)throw Error(T(331));var i=I;for(I|=4,A=e.current;A!==null;){var o=A,s=o.child;if(A.flags&16){var l=o.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(A=u;A!==null;){var f=A;switch(f.tag){case 0:case 11:case 15:Pr(8,f,o)}var c=f.child;if(c!==null)c.return=f,A=c;else for(;A!==null;){f=A;var p=f.sibling,m=f.return;if(xp(f),f===u){A=null;break}if(p!==null){p.return=m,A=p;break}A=m}}}var y=o.alternate;if(y!==null){var x=y.child;if(x!==null){y.child=null;do{var C=x.sibling;x.sibling=null,x=C}while(x!==null)}}A=o}}if(o.subtreeFlags&2064&&s!==null)s.return=o,A=s;else e:for(;A!==null;){if(o=A,o.flags&2048)switch(o.tag){case 0:case 11:case 15:Pr(9,o,o.return)}var g=o.sibling;if(g!==null){g.return=o.return,A=g;break e}A=o.return}}var d=e.current;for(A=d;A!==null;){s=A;var h=s.child;if(s.subtreeFlags&2064&&h!==null)h.return=s,A=h;else e:for(s=d;A!==null;){if(l=A,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:Vo(9,l)}}catch(S){Z(l,l.return,S)}if(l===s){A=null;break e}var v=l.sibling;if(v!==null){v.return=l.return,A=v;break e}A=l.return}}if(I=i,Gt(),nt&&typeof nt.onPostCommitFiberRoot=="function")try{nt.onPostCommitFiberRoot(Co,e)}catch{}r=!0}return r}finally{F=n,$e.transition=t}}return!1}function pc(e,t,n){t=Zn(n,t),t=ap(e,t,1),e=jt(e,t,1),t=xe(),e!==null&&(br(e,1,t),Ee(e,t))}function Z(e,t,n){if(e.tag===3)pc(e,e,n);else for(;t!==null;){if(t.tag===3){pc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Ot===null||!Ot.has(r))){e=Zn(n,e),e=up(t,e,1),t=jt(t,e,1),e=xe(),t!==null&&(br(t,1,e),Ee(t,e));break}}t=t.return}}function Ey(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=xe(),e.pingedLanes|=e.suspendedLanes&n,le===e&&(ue&n)===n&&(ie===4||ie===3&&(ue&130023424)===ue&&500>b()-Ra?un(e,0):Aa|=n),Ee(e,t)}function Ap(e,t){t===0&&(e.mode&1?(t=pi,pi<<=1,!(pi&130023424)&&(pi=4194304)):t=1);var n=xe();e=mt(e,t),e!==null&&(br(e,t,n),Ee(e,n))}function Dy(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Ap(e,n)}function Ly(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(T(314))}r!==null&&r.delete(t),Ap(e,n)}var Rp;Rp=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Pe.current)Ce=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ce=!1,my(e,t,n);Ce=!!(e.flags&131072)}else Ce=!1,W&&t.flags&1048576&&Nd(t,to,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Ii(e,t),e=t.pendingProps;var i=Gn(t,me.current);Hn(t,n),i=Ca(null,t,r,e,i,n);var o=Pa();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Te(r)?(o=!0,bi(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,va(t),i.updater=Ro,t.stateNode=i,i._reactInternals=t,hl(t,r,e,n),t=yl(null,t,r,!0,o,n)):(t.tag=0,W&&o&&fa(t),ve(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Ii(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=Ry(r),e=Ge(r,e),i){case 0:t=gl(null,t,r,e,n);break e;case 1:t=rc(null,t,r,e,n);break e;case 11:t=tc(null,t,r,e,n);break e;case 14:t=nc(null,t,r,Ge(r.type,e),n);break e}throw Error(T(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ge(r,i),gl(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ge(r,i),rc(e,t,r,i,n);case 3:e:{if(pp(t),e===null)throw Error(T(387));r=t.pendingProps,o=t.memoizedState,i=o.element,Bd(e,t),io(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=Zn(Error(T(423)),t),t=ic(e,t,r,n,i);break e}else if(r!==i){i=Zn(Error(T(424)),t),t=ic(e,t,r,n,i);break e}else for(Re=Nt(t.stateNode.containerInfo.firstChild),Ve=t,W=!0,Ye=null,n=Fd(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Qn(),r===i){t=gt(e,t,n);break e}ve(e,t,r,n)}t=t.child}return t;case 5:return Ud(t),e===null&&fl(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,s=i.children,sl(r,i)?s=null:o!==null&&sl(r,o)&&(t.flags|=32),dp(e,t),ve(e,t,s,n),t.child;case 6:return e===null&&fl(t),null;case 13:return hp(e,t,n);case 4:return xa(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Yn(t,null,r,n):ve(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ge(r,i),tc(e,t,r,i,n);case 7:return ve(e,t,t.pendingProps,n),t.child;case 8:return ve(e,t,t.pendingProps.children,n),t.child;case 12:return ve(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,s=i.value,z(no,r._currentValue),r._currentValue=s,o!==null)if(Je(o.value,s)){if(o.children===i.children&&!Pe.current){t=gt(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var l=o.dependencies;if(l!==null){s=o.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(o.tag===1){a=ct(-1,n&-n),a.tag=2;var u=o.updateQueue;if(u!==null){u=u.shared;var f=u.pending;f===null?a.next=a:(a.next=f.next,f.next=a),u.pending=a}}o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),dl(o.return,n,t),l.lanes|=n;break}a=a.next}}else if(o.tag===10)s=o.type===t.type?null:o.child;else if(o.tag===18){if(s=o.return,s===null)throw Error(T(341));s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),dl(s,n,t),s=o.sibling}else s=o.child;if(s!==null)s.return=o;else for(s=o;s!==null;){if(s===t){s=null;break}if(o=s.sibling,o!==null){o.return=s.return,s=o;break}s=s.return}o=s}ve(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Hn(t,n),i=He(i),r=r(i),t.flags|=1,ve(e,t,r,n),t.child;case 14:return r=t.type,i=Ge(r,t.pendingProps),i=Ge(r.type,i),nc(e,t,r,i,n);case 15:return cp(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ge(r,i),Ii(e,t),t.tag=1,Te(r)?(e=!0,bi(t)):e=!1,Hn(t,n),lp(t,r,i),hl(t,r,i,n),yl(null,t,r,!0,e,n);case 19:return mp(e,t,n);case 22:return fp(e,t,n)}throw Error(T(156,t.tag))};function Vp(e,t){return id(e,t)}function Ay(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ue(e,t,n,r){return new Ay(e,t,n,r)}function Na(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Ry(e){if(typeof e=="function")return Na(e)?1:0;if(e!=null){if(e=e.$$typeof,e===bl)return 11;if(e===ea)return 14}return 2}function Ft(e,t){var n=e.alternate;return n===null?(n=Ue(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Bi(e,t,n,r,i,o){var s=2;if(r=e,typeof e=="function")Na(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case Pn:return cn(n.children,i,o,t);case ql:s=8,i|=8;break;case Fs:return e=Ue(12,n,t,i|2),e.elementType=Fs,e.lanes=o,e;case zs:return e=Ue(13,n,t,i),e.elementType=zs,e.lanes=o,e;case Bs:return e=Ue(19,n,t,i),e.elementType=Bs,e.lanes=o,e;case Uf:return _o(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case zf:s=10;break e;case Bf:s=9;break e;case bl:s=11;break e;case ea:s=14;break e;case kt:s=16,r=null;break e}throw Error(T(130,e==null?e:typeof e,""))}return t=Ue(s,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function cn(e,t,n,r){return e=Ue(7,e,r,t),e.lanes=n,e}function _o(e,t,n,r){return e=Ue(22,e,r,t),e.elementType=Uf,e.lanes=n,e.stateNode={isHidden:!1},e}function ys(e,t,n){return e=Ue(6,e,null,t),e.lanes=n,e}function vs(e,t,n){return t=Ue(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Vy(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=qo(0),this.expirationTimes=qo(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=qo(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function ja(e,t,n,r,i,o,s,l,a){return e=new Vy(e,t,n,l,a),t===1?(t=1,o===!0&&(t|=8)):t=0,o=Ue(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},va(o),e}function My(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Cn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Mp(e){if(!e)return Ut;e=e._reactInternals;e:{if(vn(e)!==e||e.tag!==1)throw Error(T(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Te(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(T(171))}if(e.tag===1){var n=e.type;if(Te(n))return Md(e,n,t)}return t}function _p(e,t,n,r,i,o,s,l,a){return e=ja(n,r,!0,e,i,o,s,l,a),e.context=Mp(null),n=e.current,r=xe(),i=It(n),o=ct(r,i),o.callback=t!=null?t:null,jt(n,o,i),e.current.lanes=i,br(e,i,r),Ee(e,r),e}function No(e,t,n,r){var i=t.current,o=xe(),s=It(i);return n=Mp(n),t.context===null?t.context=n:t.pendingContext=n,t=ct(o,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=jt(i,t,s),e!==null&&(Ze(e,i,s,o),Ni(e,i,s)),s}function po(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function hc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Oa(e,t){hc(e,t),(e=e.alternate)&&hc(e,t)}function _y(){return null}var Np=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ia(e){this._internalRoot=e}jo.prototype.render=Ia.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(T(409));No(e,t,null,null)};jo.prototype.unmount=Ia.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;gn(function(){No(null,e,null,null)}),t[ht]=null}};function jo(e){this._internalRoot=e}jo.prototype.unstable_scheduleHydration=function(e){if(e){var t=fd();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Tt.length&&t!==0&&t<Tt[n].priority;n++);Tt.splice(n,0,e),n===0&&pd(e)}};function Fa(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Oo(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function mc(){}function Ny(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var u=po(s);o.call(u)}}var s=_p(t,r,e,0,null,!1,!1,"",mc);return e._reactRootContainer=s,e[ht]=s.current,Br(e.nodeType===8?e.parentNode:e),gn(),s}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var l=r;r=function(){var u=po(a);l.call(u)}}var a=ja(e,0,!1,null,null,!1,!1,"",mc);return e._reactRootContainer=a,e[ht]=a.current,Br(e.nodeType===8?e.parentNode:e),gn(function(){No(t,a,n,r)}),a}function Io(e,t,n,r,i){var o=n._reactRootContainer;if(o){var s=o;if(typeof i=="function"){var l=i;i=function(){var a=po(s);l.call(a)}}No(t,s,e,i)}else s=Ny(n,t,e,i,r);return po(s)}ud=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=mr(t.pendingLanes);n!==0&&(ra(t,n|1),Ee(t,b()),!(I&6)&&(Jn=b()+500,Gt()))}break;case 13:gn(function(){var r=mt(e,1);if(r!==null){var i=xe();Ze(r,e,1,i)}}),Oa(e,1)}};ia=function(e){if(e.tag===13){var t=mt(e,134217728);if(t!==null){var n=xe();Ze(t,e,134217728,n)}Oa(e,134217728)}};cd=function(e){if(e.tag===13){var t=It(e),n=mt(e,t);if(n!==null){var r=xe();Ze(n,e,t,r)}Oa(e,t)}};fd=function(){return F};dd=function(e,t){var n=F;try{return F=e,t()}finally{F=n}};Zs=function(e,t,n){switch(t){case"input":if(Hs(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Do(r);if(!i)throw Error(T(90));Hf(r),Hs(r,i)}}}break;case"textarea":Kf(e,n);break;case"select":t=n.value,t!=null&&zn(e,!!n.multiple,t,!1)}};qf=Va;bf=gn;var jy={usingClientEntryPoint:!1,Events:[ti,Ln,Do,Zf,Jf,Va]},cr={findFiberByHostInstance:on,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Oy={bundleType:cr.bundleType,version:cr.version,rendererPackageName:cr.rendererPackageName,rendererConfig:cr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:vt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=nd(e),e===null?null:e.stateNode},findFiberByHostInstance:cr.findFiberByHostInstance||_y,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Pi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Pi.isDisabled&&Pi.supportsFiber)try{Co=Pi.inject(Oy),nt=Pi}catch{}}Ne.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=jy;Ne.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Fa(t))throw Error(T(200));return My(e,t,null,n)};Ne.createRoot=function(e,t){if(!Fa(e))throw Error(T(299));var n=!1,r="",i=Np;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=ja(e,1,!1,null,null,n,!1,r,i),e[ht]=t.current,Br(e.nodeType===8?e.parentNode:e),new Ia(t)};Ne.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(T(188)):(e=Object.keys(e).join(","),Error(T(268,e)));return e=nd(t),e=e===null?null:e.stateNode,e};Ne.flushSync=function(e){return gn(e)};Ne.hydrate=function(e,t,n){if(!Oo(t))throw Error(T(200));return Io(null,e,t,!0,n)};Ne.hydrateRoot=function(e,t,n){if(!Fa(e))throw Error(T(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",s=Np;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=_p(t,null,e,1,n!=null?n:null,i,!1,o,s),e[ht]=t.current,Br(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new jo(t)};Ne.render=function(e,t,n){if(!Oo(t))throw Error(T(200));return Io(null,e,t,!1,n)};Ne.unmountComponentAtNode=function(e){if(!Oo(e))throw Error(T(40));return e._reactRootContainer?(gn(function(){Io(null,null,e,!1,function(){e._reactRootContainer=null,e[ht]=null})}),!0):!1};Ne.unstable_batchedUpdates=Va;Ne.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Oo(n))throw Error(T(200));if(e==null||e._reactInternals===void 0)throw Error(T(38));return Io(e,t,n,!1,r)};Ne.version="18.3.1-next-f1338f8080-20240426";function jp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(jp)}catch(e){console.error(e)}}jp(),jf.exports=Ne;var Iy=jf.exports,gc=Iy;Os.createRoot=gc.createRoot,Os.hydrateRoot=gc.hydrateRoot;function Fy(){return window.crypto.getRandomValues(new Uint32Array(1))[0]}function Al(e,t=!1){const n=Fy(),r=`_${n}`;return Object.defineProperty(window,r,{value:i=>(t&&Reflect.deleteProperty(window,r),e==null?void 0:e(i)),writable:!1,configurable:!0}),n}async function rn(e,t={}){return new Promise((n,r)=>{const i=Al(s=>{n(s),Reflect.deleteProperty(window,`_${o}`)},!0),o=Al(s=>{r(s),Reflect.deleteProperty(window,`_${i}`)},!0);window.__TAURI_IPC__({cmd:e,callback:i,error:o,...t})})}async function Op(e){return rn("tauri",e)}async function zy(e,t){return Op({__tauriModule:"Event",message:{cmd:"unlisten",event:e,eventId:t}})}async function By(e,t,n){return Op({__tauriModule:"Event",message:{cmd:"listen",event:e,windowLabel:t,handler:Al(n)}}).then(r=>async()=>zy(e,r))}var yc;(function(e){e.WINDOW_RESIZED="tauri://resize",e.WINDOW_MOVED="tauri://move",e.WINDOW_CLOSE_REQUESTED="tauri://close-requested",e.WINDOW_CREATED="tauri://window-created",e.WINDOW_DESTROYED="tauri://destroyed",e.WINDOW_FOCUS="tauri://focus",e.WINDOW_BLUR="tauri://blur",e.WINDOW_SCALE_FACTOR_CHANGED="tauri://scale-change",e.WINDOW_THEME_CHANGED="tauri://theme-changed",e.WINDOW_FILE_DROP="tauri://file-drop",e.WINDOW_FILE_DROP_HOVER="tauri://file-drop-hover",e.WINDOW_FILE_DROP_CANCELLED="tauri://file-drop-cancelled",e.MENU="tauri://menu",e.CHECK_UPDATE="tauri://update",e.UPDATE_AVAILABLE="tauri://update-available",e.INSTALL_UPDATE="tauri://update-install",e.STATUS_UPDATE="tauri://update-status",e.DOWNLOAD_PROGRESS="tauri://update-download-progress"})(yc||(yc={}));async function xs(e,t){return By(e,null,t)}const Ip=k.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),Fo=k.createContext({}),zo=k.createContext(null),Bo=typeof document<"u",za=Bo?k.useLayoutEffect:k.useEffect,Fp=k.createContext({strict:!1}),Ba=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),Uy="framerAppearId",zp="data-"+Ba(Uy);function $y(e,t,n,r){const{visualElement:i}=k.useContext(Fo),o=k.useContext(Fp),s=k.useContext(zo),l=k.useContext(Ip).reducedMotion,a=k.useRef();r=r||o.renderer,!a.current&&r&&(a.current=r(e,{visualState:t,parent:i,props:n,presenceContext:s,blockInitialAnimation:s?s.initial===!1:!1,reducedMotionConfig:l}));const u=a.current;k.useInsertionEffect(()=>{u&&u.update(n,s)});const f=k.useRef(!!(n[zp]&&!window.HandoffComplete));return za(()=>{u&&(u.render(),f.current&&u.animationState&&u.animationState.animateChanges())}),k.useEffect(()=>{u&&(u.updateFeatures(),!f.current&&u.animationState&&u.animationState.animateChanges(),f.current&&(f.current=!1,window.HandoffComplete=!0))}),u}function jn(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function Hy(e,t,n){return k.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):jn(n)&&(n.current=r))},[t])}function Xr(e){return typeof e=="string"||Array.isArray(e)}function Uo(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Ua=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],$a=["initial",...Ua];function $o(e){return Uo(e.animate)||$a.some(t=>Xr(e[t]))}function Bp(e){return!!($o(e)||e.variants)}function Wy(e,t){if($o(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Xr(n)?n:void 0,animate:Xr(r)?r:void 0}}return e.inherit!==!1?t:{}}function Ky(e){const{initial:t,animate:n}=Wy(e,k.useContext(Fo));return k.useMemo(()=>({initial:t,animate:n}),[vc(t),vc(n)])}function vc(e){return Array.isArray(e)?e.join(" "):e}const xc={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Zr={};for(const e in xc)Zr[e]={isEnabled:t=>xc[e].some(n=>!!t[n])};function Gy(e){for(const t in e)Zr[t]={...Zr[t],...e[t]}}const Ha=k.createContext({}),Up=k.createContext({}),Qy=Symbol.for("motionComponentSymbol");function Yy({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){e&&Gy(e);function o(l,a){let u;const f={...k.useContext(Ip),...l,layoutId:Xy(l)},{isStatic:c}=f,p=Ky(l),m=r(l,c);if(!c&&Bo){p.visualElement=$y(i,m,f,t);const y=k.useContext(Up),x=k.useContext(Fp).strict;p.visualElement&&(u=p.visualElement.loadFeatures(f,x,e,y))}return k.createElement(Fo.Provider,{value:p},u&&p.visualElement?k.createElement(u,{visualElement:p.visualElement,...f}):null,n(i,l,Hy(m,p.visualElement,a),m,c,p.visualElement))}const s=k.forwardRef(o);return s[Qy]=i,s}function Xy({layoutId:e}){const t=k.useContext(Ha).id;return t&&e!==void 0?t+"-"+e:e}function Zy(e){function t(r,i={}){return Yy(e(r,i))}if(typeof Proxy>"u")return t;const n=new Map;return new Proxy(t,{get:(r,i)=>(n.has(i)||n.set(i,t(i)),n.get(i))})}const Jy=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Wa(e){return typeof e!="string"||e.includes("-")?!1:!!(Jy.indexOf(e)>-1||/[A-Z]/.test(e))}const ho={};function qy(e){Object.assign(ho,e)}const ri=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],xn=new Set(ri);function $p(e,{layout:t,layoutId:n}){return xn.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!ho[e]||e==="opacity")}const De=e=>!!(e&&e.getVelocity),by={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},ev=ri.length;function tv(e,{enableHardwareAcceleration:t=!0,allowTransformNone:n=!0},r,i){let o="";for(let s=0;s<ev;s++){const l=ri[s];if(e[l]!==void 0){const a=by[l]||l;o+=`${a}(${e[l]}) `}}return t&&!e.z&&(o+="translateZ(0)"),o=o.trim(),i?o=i(e,r?"":o):n&&r&&(o="none"),o}const Hp=e=>t=>typeof t=="string"&&t.startsWith(e),Wp=Hp("--"),Rl=Hp("var(--"),nv=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,rv=(e,t)=>t&&typeof e=="number"?t.transform(e):e,$t=(e,t,n)=>Math.min(Math.max(n,e),t),Sn={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},Dr={...Sn,transform:e=>$t(0,1,e)},Ti={...Sn,default:1},Lr=e=>Math.round(e*1e5)/1e5,Ho=/(-)?([\d]*\.?[\d])+/g,Kp=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,iv=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function ii(e){return typeof e=="string"}const oi=e=>({test:t=>ii(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),wt=oi("deg"),it=oi("%"),M=oi("px"),ov=oi("vh"),sv=oi("vw"),Sc={...it,parse:e=>it.parse(e)/100,transform:e=>it.transform(e*100)},wc={...Sn,transform:Math.round},Gp={borderWidth:M,borderTopWidth:M,borderRightWidth:M,borderBottomWidth:M,borderLeftWidth:M,borderRadius:M,radius:M,borderTopLeftRadius:M,borderTopRightRadius:M,borderBottomRightRadius:M,borderBottomLeftRadius:M,width:M,maxWidth:M,height:M,maxHeight:M,size:M,top:M,right:M,bottom:M,left:M,padding:M,paddingTop:M,paddingRight:M,paddingBottom:M,paddingLeft:M,margin:M,marginTop:M,marginRight:M,marginBottom:M,marginLeft:M,rotate:wt,rotateX:wt,rotateY:wt,rotateZ:wt,scale:Ti,scaleX:Ti,scaleY:Ti,scaleZ:Ti,skew:wt,skewX:wt,skewY:wt,distance:M,translateX:M,translateY:M,translateZ:M,x:M,y:M,z:M,perspective:M,transformPerspective:M,opacity:Dr,originX:Sc,originY:Sc,originZ:M,zIndex:wc,fillOpacity:Dr,strokeOpacity:Dr,numOctaves:wc};function Ka(e,t,n,r){const{style:i,vars:o,transform:s,transformOrigin:l}=e;let a=!1,u=!1,f=!0;for(const c in t){const p=t[c];if(Wp(c)){o[c]=p;continue}const m=Gp[c],y=rv(p,m);if(xn.has(c)){if(a=!0,s[c]=y,!f)continue;p!==(m.default||0)&&(f=!1)}else c.startsWith("origin")?(u=!0,l[c]=y):i[c]=y}if(t.transform||(a||r?i.transform=tv(e.transform,n,f,r):i.transform&&(i.transform="none")),u){const{originX:c="50%",originY:p="50%",originZ:m=0}=l;i.transformOrigin=`${c} ${p} ${m}`}}const Ga=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Qp(e,t,n){for(const r in t)!De(t[r])&&!$p(r,n)&&(e[r]=t[r])}function lv({transformTemplate:e},t,n){return k.useMemo(()=>{const r=Ga();return Ka(r,t,{enableHardwareAcceleration:!n},e),Object.assign({},r.vars,r.style)},[t])}function av(e,t,n){const r=e.style||{},i={};return Qp(i,r,e),Object.assign(i,lv(e,t,n)),e.transformValues?e.transformValues(i):i}function uv(e,t,n){const r={},i=av(e,t,n);return e.drag&&e.dragListener!==!1&&(r.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=i,r}const cv=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function mo(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||cv.has(e)}let Yp=e=>!mo(e);function fv(e){e&&(Yp=t=>t.startsWith("on")?!mo(t):e(t))}try{fv(require("@emotion/is-prop-valid").default)}catch{}function dv(e,t,n){const r={};for(const i in e)i==="values"&&typeof e.values=="object"||(Yp(i)||n===!0&&mo(i)||!t&&!mo(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}function kc(e,t,n){return typeof e=="string"?e:M.transform(t+n*e)}function pv(e,t,n){const r=kc(t,e.x,e.width),i=kc(n,e.y,e.height);return`${r} ${i}`}const hv={offset:"stroke-dashoffset",array:"stroke-dasharray"},mv={offset:"strokeDashoffset",array:"strokeDasharray"};function gv(e,t,n=1,r=0,i=!0){e.pathLength=1;const o=i?hv:mv;e[o.offset]=M.transform(-r);const s=M.transform(t),l=M.transform(n);e[o.array]=`${s} ${l}`}function Qa(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:o,pathLength:s,pathSpacing:l=1,pathOffset:a=0,...u},f,c,p){if(Ka(e,u,f,p),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:m,style:y,dimensions:x}=e;m.transform&&(x&&(y.transform=m.transform),delete m.transform),x&&(i!==void 0||o!==void 0||y.transform)&&(y.transformOrigin=pv(x,i!==void 0?i:.5,o!==void 0?o:.5)),t!==void 0&&(m.x=t),n!==void 0&&(m.y=n),r!==void 0&&(m.scale=r),s!==void 0&&gv(m,s,l,a,!1)}const Xp=()=>({...Ga(),attrs:{}}),Ya=e=>typeof e=="string"&&e.toLowerCase()==="svg";function yv(e,t,n,r){const i=k.useMemo(()=>{const o=Xp();return Qa(o,t,{enableHardwareAcceleration:!1},Ya(r),e.transformTemplate),{...o.attrs,style:{...o.style}}},[t]);if(e.style){const o={};Qp(o,e.style,e),i.style={...o,...i.style}}return i}function vv(e=!1){return(n,r,i,{latestValues:o},s)=>{const a=(Wa(n)?yv:uv)(r,o,s,n),f={...dv(r,typeof n=="string",e),...a,ref:i},{children:c}=r,p=k.useMemo(()=>De(c)?c.get():c,[c]);return k.createElement(n,{...f,children:p})}}function Zp(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const o in n)e.style.setProperty(o,n[o])}const Jp=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function qp(e,t,n,r){Zp(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(Jp.has(i)?i:Ba(i),t.attrs[i])}function Xa(e,t){const{style:n}=e,r={};for(const i in n)(De(n[i])||t.style&&De(t.style[i])||$p(i,e))&&(r[i]=n[i]);return r}function bp(e,t){const n=Xa(e,t);for(const r in e)if(De(e[r])||De(t[r])){const i=ri.indexOf(r)!==-1?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r;n[i]=e[r]}return n}function Za(e,t,n,r={},i={}){return typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),t}function eh(e){const t=k.useRef(null);return t.current===null&&(t.current=e()),t.current}const go=e=>Array.isArray(e),xv=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),Sv=e=>go(e)?e[e.length-1]||0:e;function Ui(e){const t=De(e)?e.get():e;return xv(t)?t.toValue():t}function wv({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,i,o){const s={latestValues:kv(r,i,o,e),renderState:t()};return n&&(s.mount=l=>n(r,l,s)),s}const th=e=>(t,n)=>{const r=k.useContext(Fo),i=k.useContext(zo),o=()=>wv(e,t,r,i);return n?o():eh(o)};function kv(e,t,n,r){const i={},o=r(e,{});for(const p in o)i[p]=Ui(o[p]);let{initial:s,animate:l}=e;const a=$o(e),u=Bp(e);t&&u&&!a&&e.inherit!==!1&&(s===void 0&&(s=t.initial),l===void 0&&(l=t.animate));let f=n?n.initial===!1:!1;f=f||s===!1;const c=f?l:s;return c&&typeof c!="boolean"&&!Uo(c)&&(Array.isArray(c)?c:[c]).forEach(m=>{const y=Za(e,m);if(!y)return;const{transitionEnd:x,transition:C,...g}=y;for(const d in g){let h=g[d];if(Array.isArray(h)){const v=f?h.length-1:0;h=h[v]}h!==null&&(i[d]=h)}for(const d in x)i[d]=x[d]}),i}const ee=e=>e;class Cc{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){const n=this.order.indexOf(t);n!==-1&&(this.order.splice(n,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}function Cv(e){let t=new Cc,n=new Cc,r=0,i=!1,o=!1;const s=new WeakSet,l={schedule:(a,u=!1,f=!1)=>{const c=f&&i,p=c?t:n;return u&&s.add(a),p.add(a)&&c&&i&&(r=t.order.length),a},cancel:a=>{n.remove(a),s.delete(a)},process:a=>{if(i){o=!0;return}if(i=!0,[t,n]=[n,t],n.clear(),r=t.order.length,r)for(let u=0;u<r;u++){const f=t.order[u];f(a),s.has(f)&&(l.schedule(f),e())}i=!1,o&&(o=!1,l.process(a))}};return l}const Ei=["prepare","read","update","preRender","render","postRender"],Pv=40;function Tv(e,t){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=Ei.reduce((c,p)=>(c[p]=Cv(()=>n=!0),c),{}),s=c=>o[c].process(i),l=()=>{const c=performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(c-i.timestamp,Pv),1),i.timestamp=c,i.isProcessing=!0,Ei.forEach(s),i.isProcessing=!1,n&&t&&(r=!1,e(l))},a=()=>{n=!0,r=!0,i.isProcessing||e(l)};return{schedule:Ei.reduce((c,p)=>{const m=o[p];return c[p]=(y,x=!1,C=!1)=>(n||a(),m.schedule(y,x,C)),c},{}),cancel:c=>Ei.forEach(p=>o[p].cancel(c)),state:i,steps:o}}const{schedule:B,cancel:yt,state:pe,steps:Ss}=Tv(typeof requestAnimationFrame<"u"?requestAnimationFrame:ee,!0),Ev={useVisualState:th({scrapeMotionValuesFromProps:bp,createRenderState:Xp,onMount:(e,t,{renderState:n,latestValues:r})=>{B.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),B.render(()=>{Qa(n,r,{enableHardwareAcceleration:!1},Ya(t.tagName),e.transformTemplate),qp(t,n)})}})},Dv={useVisualState:th({scrapeMotionValuesFromProps:Xa,createRenderState:Ga})};function Lv(e,{forwardMotionProps:t=!1},n,r){return{...Wa(e)?Ev:Dv,preloadedFeatures:n,useRender:vv(t),createVisualElement:r,Component:e}}function ut(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const nh=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function Wo(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}const Av=e=>t=>nh(t)&&e(t,Wo(t));function ft(e,t,n,r){return ut(e,t,Av(n),r)}const Rv=(e,t)=>n=>t(e(n)),zt=(...e)=>e.reduce(Rv);function rh(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const Pc=rh("dragHorizontal"),Tc=rh("dragVertical");function ih(e){let t=!1;if(e==="y")t=Tc();else if(e==="x")t=Pc();else{const n=Pc(),r=Tc();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function oh(){const e=ih(!0);return e?(e(),!1):!0}class Qt{constructor(t){this.isMounted=!1,this.node=t}update(){}}function Ec(e,t){const n="pointer"+(t?"enter":"leave"),r="onHover"+(t?"Start":"End"),i=(o,s)=>{if(o.pointerType==="touch"||oh())return;const l=e.getProps();e.animationState&&l.whileHover&&e.animationState.setActive("whileHover",t),l[r]&&B.update(()=>l[r](o,s))};return ft(e.current,n,i,{passive:!e.getProps()[r]})}class Vv extends Qt{mount(){this.unmount=zt(Ec(this.node,!0),Ec(this.node,!1))}unmount(){}}class Mv extends Qt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=zt(ut(this.node.current,"focus",()=>this.onFocus()),ut(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const sh=(e,t)=>t?e===t?!0:sh(e,t.parentElement):!1;function ws(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,Wo(n))}class _v extends Qt{constructor(){super(...arguments),this.removeStartListeners=ee,this.removeEndListeners=ee,this.removeAccessibleListeners=ee,this.startPointerPress=(t,n)=>{if(this.isPressing)return;this.removeEndListeners();const r=this.node.getProps(),o=ft(window,"pointerup",(l,a)=>{if(!this.checkPressEnd())return;const{onTap:u,onTapCancel:f,globalTapTarget:c}=this.node.getProps();B.update(()=>{!c&&!sh(this.node.current,l.target)?f&&f(l,a):u&&u(l,a)})},{passive:!(r.onTap||r.onPointerUp)}),s=ft(window,"pointercancel",(l,a)=>this.cancelPress(l,a),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=zt(o,s),this.startPress(t,n)},this.startAccessiblePress=()=>{const t=o=>{if(o.key!=="Enter"||this.isPressing)return;const s=l=>{l.key!=="Enter"||!this.checkPressEnd()||ws("up",(a,u)=>{const{onTap:f}=this.node.getProps();f&&B.update(()=>f(a,u))})};this.removeEndListeners(),this.removeEndListeners=ut(this.node.current,"keyup",s),ws("down",(l,a)=>{this.startPress(l,a)})},n=ut(this.node.current,"keydown",t),r=()=>{this.isPressing&&ws("cancel",(o,s)=>this.cancelPress(o,s))},i=ut(this.node.current,"blur",r);this.removeAccessibleListeners=zt(n,i)}}startPress(t,n){this.isPressing=!0;const{onTapStart:r,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&B.update(()=>r(t,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!oh()}cancelPress(t,n){if(!this.checkPressEnd())return;const{onTapCancel:r}=this.node.getProps();r&&B.update(()=>r(t,n))}mount(){const t=this.node.getProps(),n=ft(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=ut(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=zt(n,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const Vl=new WeakMap,ks=new WeakMap,Nv=e=>{const t=Vl.get(e.target);t&&t(e)},jv=e=>{e.forEach(Nv)};function Ov({root:e,...t}){const n=e||document;ks.has(n)||ks.set(n,{});const r=ks.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(jv,{root:e,...t})),r[i]}function Iv(e,t,n){const r=Ov(t);return Vl.set(e,n),r.observe(e),()=>{Vl.delete(e),r.unobserve(e)}}const Fv={some:0,all:1};class zv extends Qt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:i="some",once:o}=t,s={root:n?n.current:void 0,rootMargin:r,threshold:typeof i=="number"?i:Fv[i]},l=a=>{const{isIntersecting:u}=a;if(this.isInView===u||(this.isInView=u,o&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:f,onViewportLeave:c}=this.node.getProps(),p=u?f:c;p&&p(a)};return Iv(this.node.current,s,l)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(Bv(t,n))&&this.startObserver()}unmount(){}}function Bv({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const Uv={inView:{Feature:zv},tap:{Feature:_v},focus:{Feature:Mv},hover:{Feature:Vv}};function lh(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function $v(e){const t={};return e.values.forEach((n,r)=>t[r]=n.get()),t}function Hv(e){const t={};return e.values.forEach((n,r)=>t[r]=n.getVelocity()),t}function Ko(e,t,n){const r=e.getProps();return Za(r,t,n!==void 0?n:r.custom,$v(e),Hv(e))}let Ja=ee;const fn=e=>e*1e3,dt=e=>e/1e3,Wv={current:!1},ah=e=>Array.isArray(e)&&typeof e[0]=="number";function uh(e){return!!(!e||typeof e=="string"&&ch[e]||ah(e)||Array.isArray(e)&&e.every(uh))}const yr=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,ch={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:yr([0,.65,.55,1]),circOut:yr([.55,0,1,.45]),backIn:yr([.31,.01,.66,-.59]),backOut:yr([.33,1.53,.69,.99])};function fh(e){if(e)return ah(e)?yr(e):Array.isArray(e)?e.map(fh):ch[e]}function Kv(e,t,n,{delay:r=0,duration:i,repeat:o=0,repeatType:s="loop",ease:l,times:a}={}){const u={[t]:n};a&&(u.offset=a);const f=fh(l);return Array.isArray(f)&&(u.easing=f),e.animate(u,{delay:r,duration:i,easing:Array.isArray(f)?"linear":f,fill:"both",iterations:o+1,direction:s==="reverse"?"alternate":"normal"})}function Gv(e,{repeat:t,repeatType:n="loop"}){const r=t&&n!=="loop"&&t%2===1?0:e.length-1;return e[r]}const dh=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,Qv=1e-7,Yv=12;function Xv(e,t,n,r,i){let o,s,l=0;do s=t+(n-t)/2,o=dh(s,r,i)-e,o>0?n=s:t=s;while(Math.abs(o)>Qv&&++l<Yv);return s}function si(e,t,n,r){if(e===t&&n===r)return ee;const i=o=>Xv(o,0,1,e,n);return o=>o===0||o===1?o:dh(i(o),t,r)}const Zv=si(.42,0,1,1),Jv=si(0,0,.58,1),ph=si(.42,0,.58,1),qv=e=>Array.isArray(e)&&typeof e[0]!="number",hh=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,mh=e=>t=>1-e(1-t),qa=e=>1-Math.sin(Math.acos(e)),gh=mh(qa),bv=hh(qa),yh=si(.33,1.53,.69,.99),ba=mh(yh),e0=hh(ba),t0=e=>(e*=2)<1?.5*ba(e):.5*(2-Math.pow(2,-10*(e-1))),n0={linear:ee,easeIn:Zv,easeInOut:ph,easeOut:Jv,circIn:qa,circInOut:bv,circOut:gh,backIn:ba,backInOut:e0,backOut:yh,anticipate:t0},Dc=e=>{if(Array.isArray(e)){Ja(e.length===4);const[t,n,r,i]=e;return si(t,n,r,i)}else if(typeof e=="string")return n0[e];return e},eu=(e,t)=>n=>!!(ii(n)&&iv.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),vh=(e,t,n)=>r=>{if(!ii(r))return r;const[i,o,s,l]=r.match(Ho);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(s),alpha:l!==void 0?parseFloat(l):1}},r0=e=>$t(0,255,e),Cs={...Sn,transform:e=>Math.round(r0(e))},an={test:eu("rgb","red"),parse:vh("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+Cs.transform(e)+", "+Cs.transform(t)+", "+Cs.transform(n)+", "+Lr(Dr.transform(r))+")"};function i0(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const Ml={test:eu("#"),parse:i0,transform:an.transform},On={test:eu("hsl","hue"),parse:vh("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+it.transform(Lr(t))+", "+it.transform(Lr(n))+", "+Lr(Dr.transform(r))+")"},ye={test:e=>an.test(e)||Ml.test(e)||On.test(e),parse:e=>an.test(e)?an.parse(e):On.test(e)?On.parse(e):Ml.parse(e),transform:e=>ii(e)?e:e.hasOwnProperty("red")?an.transform(e):On.transform(e)},Q=(e,t,n)=>-n*e+n*t+e;function Ps(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function o0({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,o=0,s=0;if(!t)i=o=s=n;else{const l=n<.5?n*(1+t):n+t-n*t,a=2*n-l;i=Ps(a,l,e+1/3),o=Ps(a,l,e),s=Ps(a,l,e-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(s*255),alpha:r}}const Ts=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},s0=[Ml,an,On],l0=e=>s0.find(t=>t.test(e));function Lc(e){const t=l0(e);let n=t.parse(e);return t===On&&(n=o0(n)),n}const xh=(e,t)=>{const n=Lc(e),r=Lc(t),i={...n};return o=>(i.red=Ts(n.red,r.red,o),i.green=Ts(n.green,r.green,o),i.blue=Ts(n.blue,r.blue,o),i.alpha=Q(n.alpha,r.alpha,o),an.transform(i))};function a0(e){var t,n;return isNaN(e)&&ii(e)&&(((t=e.match(Ho))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(Kp))===null||n===void 0?void 0:n.length)||0)>0}const Sh={regex:nv,countKey:"Vars",token:"${v}",parse:ee},wh={regex:Kp,countKey:"Colors",token:"${c}",parse:ye.parse},kh={regex:Ho,countKey:"Numbers",token:"${n}",parse:Sn.parse};function Es(e,{regex:t,countKey:n,token:r,parse:i}){const o=e.tokenised.match(t);o&&(e["num"+n]=o.length,e.tokenised=e.tokenised.replace(t,r),e.values.push(...o.map(i)))}function yo(e){const t=e.toString(),n={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&Es(n,Sh),Es(n,wh),Es(n,kh),n}function Ch(e){return yo(e).values}function Ph(e){const{values:t,numColors:n,numVars:r,tokenised:i}=yo(e),o=t.length;return s=>{let l=i;for(let a=0;a<o;a++)a<r?l=l.replace(Sh.token,s[a]):a<r+n?l=l.replace(wh.token,ye.transform(s[a])):l=l.replace(kh.token,Lr(s[a]));return l}}const u0=e=>typeof e=="number"?0:e;function c0(e){const t=Ch(e);return Ph(e)(t.map(u0))}const Ht={test:a0,parse:Ch,createTransformer:Ph,getAnimatableNone:c0},Th=(e,t)=>n=>`${n>0?t:e}`;function Eh(e,t){return typeof e=="number"?n=>Q(e,t,n):ye.test(e)?xh(e,t):e.startsWith("var(")?Th(e,t):Lh(e,t)}const Dh=(e,t)=>{const n=[...e],r=n.length,i=e.map((o,s)=>Eh(o,t[s]));return o=>{for(let s=0;s<r;s++)n[s]=i[s](o);return n}},f0=(e,t)=>{const n={...e,...t},r={};for(const i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=Eh(e[i],t[i]));return i=>{for(const o in r)n[o]=r[o](i);return n}},Lh=(e,t)=>{const n=Ht.createTransformer(t),r=yo(e),i=yo(t);return r.numVars===i.numVars&&r.numColors===i.numColors&&r.numNumbers>=i.numNumbers?zt(Dh(r.values,i.values),n):Th(e,t)},Jr=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},Ac=(e,t)=>n=>Q(e,t,n);function d0(e){return typeof e=="number"?Ac:typeof e=="string"?ye.test(e)?xh:Lh:Array.isArray(e)?Dh:typeof e=="object"?f0:Ac}function p0(e,t,n){const r=[],i=n||d0(e[0]),o=e.length-1;for(let s=0;s<o;s++){let l=i(e[s],e[s+1]);if(t){const a=Array.isArray(t)?t[s]||ee:t;l=zt(a,l)}r.push(l)}return r}function Ah(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const o=e.length;if(Ja(o===t.length),o===1)return()=>t[0];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());const s=p0(t,r,i),l=s.length,a=u=>{let f=0;if(l>1)for(;f<e.length-2&&!(u<e[f+1]);f++);const c=Jr(e[f],e[f+1],u);return s[f](c)};return n?u=>a($t(e[0],e[o-1],u)):a}function h0(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=Jr(0,t,r);e.push(Q(n,1,i))}}function m0(e){const t=[0];return h0(t,e.length-1),t}function g0(e,t){return e.map(n=>n*t)}function y0(e,t){return e.map(()=>t||ph).splice(0,e.length-1)}function vo({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const i=qv(r)?r.map(Dc):Dc(r),o={done:!1,value:t[0]},s=g0(n&&n.length===t.length?n:m0(t),e),l=Ah(s,t,{ease:Array.isArray(i)?i:y0(t,i)});return{calculatedDuration:e,next:a=>(o.value=l(a),o.done=a>=e,o)}}function Rh(e,t){return t?e*(1e3/t):0}const v0=5;function Vh(e,t,n){const r=Math.max(t-v0,0);return Rh(n-e(r),t-r)}const Ds=.001,x0=.01,S0=10,w0=.05,k0=1;function C0({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let i,o,s=1-t;s=$t(w0,k0,s),e=$t(x0,S0,dt(e)),s<1?(i=u=>{const f=u*s,c=f*e,p=f-n,m=_l(u,s),y=Math.exp(-c);return Ds-p/m*y},o=u=>{const c=u*s*e,p=c*n+n,m=Math.pow(s,2)*Math.pow(u,2)*e,y=Math.exp(-c),x=_l(Math.pow(u,2),s);return(-i(u)+Ds>0?-1:1)*((p-m)*y)/x}):(i=u=>{const f=Math.exp(-u*e),c=(u-n)*e+1;return-Ds+f*c},o=u=>{const f=Math.exp(-u*e),c=(n-u)*(e*e);return f*c});const l=5/e,a=T0(i,o,l);if(e=fn(e),isNaN(a))return{stiffness:100,damping:10,duration:e};{const u=Math.pow(a,2)*r;return{stiffness:u,damping:s*2*Math.sqrt(r*u),duration:e}}}const P0=12;function T0(e,t,n){let r=n;for(let i=1;i<P0;i++)r=r-e(r)/t(r);return r}function _l(e,t){return e*Math.sqrt(1-t*t)}const E0=["duration","bounce"],D0=["stiffness","damping","mass"];function Rc(e,t){return t.some(n=>e[n]!==void 0)}function L0(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!Rc(e,D0)&&Rc(e,E0)){const n=C0(e);t={...t,...n,mass:1},t.isResolvedFromDuration=!0}return t}function Mh({keyframes:e,restDelta:t,restSpeed:n,...r}){const i=e[0],o=e[e.length-1],s={done:!1,value:i},{stiffness:l,damping:a,mass:u,duration:f,velocity:c,isResolvedFromDuration:p}=L0({...r,velocity:-dt(r.velocity||0)}),m=c||0,y=a/(2*Math.sqrt(l*u)),x=o-i,C=dt(Math.sqrt(l/u)),g=Math.abs(x)<5;n||(n=g?.01:2),t||(t=g?.005:.5);let d;if(y<1){const h=_l(C,y);d=v=>{const S=Math.exp(-y*C*v);return o-S*((m+y*C*x)/h*Math.sin(h*v)+x*Math.cos(h*v))}}else if(y===1)d=h=>o-Math.exp(-C*h)*(x+(m+C*x)*h);else{const h=C*Math.sqrt(y*y-1);d=v=>{const S=Math.exp(-y*C*v),w=Math.min(h*v,300);return o-S*((m+y*C*x)*Math.sinh(w)+h*x*Math.cosh(w))/h}}return{calculatedDuration:p&&f||null,next:h=>{const v=d(h);if(p)s.done=h>=f;else{let S=m;h!==0&&(y<1?S=Vh(d,h,v):S=0);const w=Math.abs(S)<=n,E=Math.abs(o-v)<=t;s.done=w&&E}return s.value=s.done?o:v,s}}}function Vc({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:l,max:a,restDelta:u=.5,restSpeed:f}){const c=e[0],p={done:!1,value:c},m=P=>l!==void 0&&P<l||a!==void 0&&P>a,y=P=>l===void 0?a:a===void 0||Math.abs(l-P)<Math.abs(a-P)?l:a;let x=n*t;const C=c+x,g=s===void 0?C:s(C);g!==C&&(x=g-c);const d=P=>-x*Math.exp(-P/r),h=P=>g+d(P),v=P=>{const R=d(P),V=h(P);p.done=Math.abs(R)<=u,p.value=p.done?g:V};let S,w;const E=P=>{m(p.value)&&(S=P,w=Mh({keyframes:[p.value,y(p.value)],velocity:Vh(h,P,p.value),damping:i,stiffness:o,restDelta:u,restSpeed:f}))};return E(0),{calculatedDuration:null,next:P=>{let R=!1;return!w&&S===void 0&&(R=!0,v(P),E(P)),S!==void 0&&P>S?w.next(P-S):(!R&&v(P),p)}}}const A0=e=>{const t=({timestamp:n})=>e(n);return{start:()=>B.update(t,!0),stop:()=>yt(t),now:()=>pe.isProcessing?pe.timestamp:performance.now()}},Mc=2e4;function _c(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<Mc;)t+=n,r=e.next(t);return t>=Mc?1/0:t}const R0={decay:Vc,inertia:Vc,tween:vo,keyframes:vo,spring:Mh};function xo({autoplay:e=!0,delay:t=0,driver:n=A0,keyframes:r,type:i="keyframes",repeat:o=0,repeatDelay:s=0,repeatType:l="loop",onPlay:a,onStop:u,onComplete:f,onUpdate:c,...p}){let m=1,y=!1,x,C;const g=()=>{C=new Promise(N=>{x=N})};g();let d;const h=R0[i]||vo;let v;h!==vo&&typeof r[0]!="number"&&(v=Ah([0,100],r,{clamp:!1}),r=[0,100]);const S=h({...p,keyframes:r});let w;l==="mirror"&&(w=h({...p,keyframes:[...r].reverse(),velocity:-(p.velocity||0)}));let E="idle",P=null,R=null,V=null;S.calculatedDuration===null&&o&&(S.calculatedDuration=_c(S));const{calculatedDuration:J}=S;let K=1/0,ge=1/0;J!==null&&(K=J+s,ge=K*(o+1)-s);let oe=0;const xt=N=>{if(R===null)return;m>0&&(R=Math.min(R,N)),m<0&&(R=Math.min(N-ge/m,R)),P!==null?oe=P:oe=Math.round(N-R)*m;const H=oe-t*(m>=0?1:-1),Yt=m>=0?H<0:H>ge;oe=Math.max(H,0),E==="finished"&&P===null&&(oe=ge);let qe=oe,wn=S;if(o){const Go=Math.min(oe,ge)/K;let li=Math.floor(Go),Zt=Go%1;!Zt&&Go>=1&&(Zt=1),Zt===1&&li--,li=Math.min(li,o+1),!!(li%2)&&(l==="reverse"?(Zt=1-Zt,s&&(Zt-=s/K)):l==="mirror"&&(wn=w)),qe=$t(0,1,Zt)*K}const Le=Yt?{done:!1,value:r[0]}:wn.next(qe);v&&(Le.value=v(Le.value));let{done:Xt}=Le;!Yt&&J!==null&&(Xt=m>=0?oe>=ge:oe<=0);const cm=P===null&&(E==="finished"||E==="running"&&Xt);return c&&c(Le.value),cm&&L(),Le},q=()=>{d&&d.stop(),d=void 0},Oe=()=>{E="idle",q(),x(),g(),R=V=null},L=()=>{E="finished",f&&f(),q(),x()},_=()=>{if(y)return;d||(d=n(xt));const N=d.now();a&&a(),P!==null?R=N-P:(!R||E==="finished")&&(R=N),E==="finished"&&g(),V=R,P=null,E="running",d.start()};e&&_();const j={then(N,H){return C.then(N,H)},get time(){return dt(oe)},set time(N){N=fn(N),oe=N,P!==null||!d||m===0?P=N:R=d.now()-N/m},get duration(){const N=S.calculatedDuration===null?_c(S):S.calculatedDuration;return dt(N)},get speed(){return m},set speed(N){N===m||!d||(m=N,j.time=dt(oe))},get state(){return E},play:_,pause:()=>{E="paused",P=oe},stop:()=>{y=!0,E!=="idle"&&(E="idle",u&&u(),Oe())},cancel:()=>{V!==null&&xt(V),Oe()},complete:()=>{E="finished"},sample:N=>(R=0,xt(N))};return j}function V0(e){let t;return()=>(t===void 0&&(t=e()),t)}const M0=V0(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),_0=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),Di=10,N0=2e4,j0=(e,t)=>t.type==="spring"||e==="backgroundColor"||!uh(t.ease);function O0(e,t,{onUpdate:n,onComplete:r,...i}){if(!(M0()&&_0.has(t)&&!i.repeatDelay&&i.repeatType!=="mirror"&&i.damping!==0&&i.type!=="inertia"))return!1;let s=!1,l,a,u=!1;const f=()=>{a=new Promise(h=>{l=h})};f();let{keyframes:c,duration:p=300,ease:m,times:y}=i;if(j0(t,i)){const h=xo({...i,repeat:0,delay:0});let v={done:!1,value:c[0]};const S=[];let w=0;for(;!v.done&&w<N0;)v=h.sample(w),S.push(v.value),w+=Di;y=void 0,c=S,p=w-Di,m="linear"}const x=Kv(e.owner.current,t,c,{...i,duration:p,ease:m,times:y}),C=()=>{u=!1,x.cancel()},g=()=>{u=!0,B.update(C),l(),f()};return x.onfinish=()=>{u||(e.set(Gv(c,i)),r&&r(),g())},{then(h,v){return a.then(h,v)},attachTimeline(h){return x.timeline=h,x.onfinish=null,ee},get time(){return dt(x.currentTime||0)},set time(h){x.currentTime=fn(h)},get speed(){return x.playbackRate},set speed(h){x.playbackRate=h},get duration(){return dt(p)},play:()=>{s||(x.play(),yt(C))},pause:()=>x.pause(),stop:()=>{if(s=!0,x.playState==="idle")return;const{currentTime:h}=x;if(h){const v=xo({...i,autoplay:!1});e.setWithVelocity(v.sample(h-Di).value,v.sample(h).value,Di)}g()},complete:()=>{u||x.finish()},cancel:g}}function I0({keyframes:e,delay:t,onUpdate:n,onComplete:r}){const i=()=>(n&&n(e[e.length-1]),r&&r(),{time:0,speed:1,duration:0,play:ee,pause:ee,stop:ee,then:o=>(o(),Promise.resolve()),cancel:ee,complete:ee});return t?xo({keyframes:[0,1],duration:0,delay:t,onComplete:i}):i()}const F0={type:"spring",stiffness:500,damping:25,restSpeed:10},z0=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),B0={type:"keyframes",duration:.8},U0={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},$0=(e,{keyframes:t})=>t.length>2?B0:xn.has(e)?e.startsWith("scale")?z0(t[1]):F0:U0,Nl=(e,t)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(Ht.test(t)||t==="0")&&!t.startsWith("url(")),H0=new Set(["brightness","contrast","saturate","opacity"]);function W0(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(Ho)||[];if(!r)return e;const i=n.replace(r,"");let o=H0.has(t)?1:0;return r!==n&&(o*=100),t+"("+o+i+")"}const K0=/([a-z-]*)\(.*?\)/g,jl={...Ht,getAnimatableNone:e=>{const t=e.match(K0);return t?t.map(W0).join(" "):e}},G0={...Gp,color:ye,backgroundColor:ye,outlineColor:ye,fill:ye,stroke:ye,borderColor:ye,borderTopColor:ye,borderRightColor:ye,borderBottomColor:ye,borderLeftColor:ye,filter:jl,WebkitFilter:jl},tu=e=>G0[e];function _h(e,t){let n=tu(e);return n!==jl&&(n=Ht),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const Nh=e=>/^0[^.\s]+$/.test(e);function Q0(e){if(typeof e=="number")return e===0;if(e!==null)return e==="none"||e==="0"||Nh(e)}function Y0(e,t,n,r){const i=Nl(t,n);let o;Array.isArray(n)?o=[...n]:o=[null,n];const s=r.from!==void 0?r.from:e.get();let l;const a=[];for(let u=0;u<o.length;u++)o[u]===null&&(o[u]=u===0?s:o[u-1]),Q0(o[u])&&a.push(u),typeof o[u]=="string"&&o[u]!=="none"&&o[u]!=="0"&&(l=o[u]);if(i&&a.length&&l)for(let u=0;u<a.length;u++){const f=a[u];o[f]=_h(t,l)}return o}function X0({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:l,from:a,elapsed:u,...f}){return!!Object.keys(f).length}function nu(e,t){return e[t]||e.default||e}const Z0={skipAnimations:!1},ru=(e,t,n,r={})=>i=>{const o=nu(r,e)||{},s=o.delay||r.delay||0;let{elapsed:l=0}=r;l=l-fn(s);const a=Y0(t,e,n,o),u=a[0],f=a[a.length-1],c=Nl(e,u),p=Nl(e,f);let m={keyframes:a,velocity:t.getVelocity(),ease:"easeOut",...o,delay:-l,onUpdate:y=>{t.set(y),o.onUpdate&&o.onUpdate(y)},onComplete:()=>{i(),o.onComplete&&o.onComplete()}};if(X0(o)||(m={...m,...$0(e,m)}),m.duration&&(m.duration=fn(m.duration)),m.repeatDelay&&(m.repeatDelay=fn(m.repeatDelay)),!c||!p||Wv.current||o.type===!1||Z0.skipAnimations)return I0(m);if(!r.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){const y=O0(t,e,m);if(y)return y}return xo(m)};function So(e){return!!(De(e)&&e.add)}const jh=e=>/^\-?\d*\.?\d+$/.test(e);function iu(e,t){e.indexOf(t)===-1&&e.push(t)}function ou(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class su{constructor(){this.subscriptions=[]}add(t){return iu(this.subscriptions,t),()=>ou(this.subscriptions,t)}notify(t,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,n,r);else for(let o=0;o<i;o++){const s=this.subscriptions[o];s&&s(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const J0=e=>!isNaN(parseFloat(e));class q0{constructor(t,n={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,i=!0)=>{this.prev=this.current,this.current=r;const{delta:o,timestamp:s}=pe;this.lastUpdated!==s&&(this.timeDelta=o,this.lastUpdated=s,B.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>B.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=J0(this.current),this.owner=n.owner}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new su);const r=this.events[t].add(n);return t==="change"?()=>{r(),B.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=t,this.timeDelta=r}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?Rh(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function qn(e,t){return new q0(e,t)}const Oh=e=>t=>t.test(e),b0={test:e=>e==="auto",parse:e=>e},Ih=[Sn,M,it,wt,sv,ov,b0],fr=e=>Ih.find(Oh(e)),e1=[...Ih,ye,Ht],t1=e=>e1.find(Oh(e));function n1(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,qn(n))}function r1(e,t){const n=Ko(e,t);let{transitionEnd:r={},transition:i={},...o}=n?e.makeTargetAnimatable(n,!1):{};o={...o,...r};for(const s in o){const l=Sv(o[s]);n1(e,s,l)}}function i1(e,t,n){var r,i;const o=Object.keys(t).filter(l=>!e.hasValue(l)),s=o.length;if(s)for(let l=0;l<s;l++){const a=o[l],u=t[a];let f=null;Array.isArray(u)&&(f=u[0]),f===null&&(f=(i=(r=n[a])!==null&&r!==void 0?r:e.readValue(a))!==null&&i!==void 0?i:t[a]),f!=null&&(typeof f=="string"&&(jh(f)||Nh(f))?f=parseFloat(f):!t1(f)&&Ht.test(u)&&(f=_h(a,u)),e.addValue(a,qn(f,{owner:e})),n[a]===void 0&&(n[a]=f),f!==null&&e.setBaseTarget(a,f))}}function o1(e,t){return t?(t[e]||t.default||t).from:void 0}function s1(e,t,n){const r={};for(const i in e){const o=o1(i,t);if(o!==void 0)r[i]=o;else{const s=n.getValue(i);s&&(r[i]=s.get())}}return r}function l1({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function a1(e,t){const n=e.get();if(Array.isArray(t)){for(let r=0;r<t.length;r++)if(t[r]!==n)return!0}else return n!==t}function Fh(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:s,...l}=e.makeTargetAnimatable(t);const a=e.getValue("willChange");r&&(o=r);const u=[],f=i&&e.animationState&&e.animationState.getState()[i];for(const c in l){const p=e.getValue(c),m=l[c];if(!p||m===void 0||f&&l1(f,c))continue;const y={delay:n,elapsed:0,...nu(o||{},c)};if(window.HandoffAppearAnimations){const g=e.getProps()[zp];if(g){const d=window.HandoffAppearAnimations(g,c,p,B);d!==null&&(y.elapsed=d,y.isHandoff=!0)}}let x=!y.isHandoff&&!a1(p,m);if(y.type==="spring"&&(p.getVelocity()||y.velocity)&&(x=!1),p.animation&&(x=!1),x)continue;p.start(ru(c,p,m,e.shouldReduceMotion&&xn.has(c)?{type:!1}:y));const C=p.animation;So(a)&&(a.add(c),C.then(()=>a.remove(c))),u.push(C)}return s&&Promise.all(u).then(()=>{s&&r1(e,s)}),u}function Ol(e,t,n={}){const r=Ko(e,t,n.custom);let{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);const o=r?()=>Promise.all(Fh(e,r,n)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(a=0)=>{const{delayChildren:u=0,staggerChildren:f,staggerDirection:c}=i;return u1(e,t,u+a,f,c,n)}:()=>Promise.resolve(),{when:l}=i;if(l){const[a,u]=l==="beforeChildren"?[o,s]:[s,o];return a().then(()=>u())}else return Promise.all([o(),s(n.delay)])}function u1(e,t,n=0,r=0,i=1,o){const s=[],l=(e.variantChildren.size-1)*r,a=i===1?(u=0)=>u*r:(u=0)=>l-u*r;return Array.from(e.variantChildren).sort(c1).forEach((u,f)=>{u.notify("AnimationStart",t),s.push(Ol(u,t,{...o,delay:n+a(f)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(s)}function c1(e,t){return e.sortNodePosition(t)}function f1(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const i=t.map(o=>Ol(e,o,n));r=Promise.all(i)}else if(typeof t=="string")r=Ol(e,t,n);else{const i=typeof t=="function"?Ko(e,t,n.custom):t;r=Promise.all(Fh(e,i,n))}return r.then(()=>e.notify("AnimationComplete",t))}const d1=[...Ua].reverse(),p1=Ua.length;function h1(e){return t=>Promise.all(t.map(({animation:n,options:r})=>f1(e,n,r)))}function m1(e){let t=h1(e);const n=y1();let r=!0;const i=(a,u)=>{const f=Ko(e,u);if(f){const{transition:c,transitionEnd:p,...m}=f;a={...a,...m,...p}}return a};function o(a){t=a(e)}function s(a,u){const f=e.getProps(),c=e.getVariantContext(!0)||{},p=[],m=new Set;let y={},x=1/0;for(let g=0;g<p1;g++){const d=d1[g],h=n[d],v=f[d]!==void 0?f[d]:c[d],S=Xr(v),w=d===u?h.isActive:null;w===!1&&(x=g);let E=v===c[d]&&v!==f[d]&&S;if(E&&r&&e.manuallyAnimateOnMount&&(E=!1),h.protectedKeys={...y},!h.isActive&&w===null||!v&&!h.prevProp||Uo(v)||typeof v=="boolean")continue;let R=g1(h.prevProp,v)||d===u&&h.isActive&&!E&&S||g>x&&S,V=!1;const J=Array.isArray(v)?v:[v];let K=J.reduce(i,{});w===!1&&(K={});const{prevResolvedValues:ge={}}=h,oe={...ge,...K},xt=q=>{R=!0,m.has(q)&&(V=!0,m.delete(q)),h.needsAnimating[q]=!0};for(const q in oe){const Oe=K[q],L=ge[q];if(y.hasOwnProperty(q))continue;let _=!1;go(Oe)&&go(L)?_=!lh(Oe,L):_=Oe!==L,_?Oe!==void 0?xt(q):m.add(q):Oe!==void 0&&m.has(q)?xt(q):h.protectedKeys[q]=!0}h.prevProp=v,h.prevResolvedValues=K,h.isActive&&(y={...y,...K}),r&&e.blockInitialAnimation&&(R=!1),R&&(!E||V)&&p.push(...J.map(q=>({animation:q,options:{type:d,...a}})))}if(m.size){const g={};m.forEach(d=>{const h=e.getBaseTarget(d);h!==void 0&&(g[d]=h)}),p.push({animation:g})}let C=!!p.length;return r&&(f.initial===!1||f.initial===f.animate)&&!e.manuallyAnimateOnMount&&(C=!1),r=!1,C?t(p):Promise.resolve()}function l(a,u,f){var c;if(n[a].isActive===u)return Promise.resolve();(c=e.variantChildren)===null||c===void 0||c.forEach(m=>{var y;return(y=m.animationState)===null||y===void 0?void 0:y.setActive(a,u)}),n[a].isActive=u;const p=s(f,a);for(const m in n)n[m].protectedKeys={};return p}return{animateChanges:s,setActive:l,setAnimateFunction:o,getState:()=>n}}function g1(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!lh(t,e):!1}function Jt(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function y1(){return{animate:Jt(!0),whileInView:Jt(),whileHover:Jt(),whileTap:Jt(),whileDrag:Jt(),whileFocus:Jt(),exit:Jt()}}class v1 extends Qt{constructor(t){super(t),t.animationState||(t.animationState=m1(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();this.unmount(),Uo(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){}}let x1=0;class S1 extends Qt{constructor(){super(...arguments),this.id=x1++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n,custom:r}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;const o=this.node.animationState.setActive("exit",!t,{custom:r!=null?r:this.node.getProps().custom});n&&!t&&o.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const w1={animation:{Feature:v1},exit:{Feature:S1}},Nc=(e,t)=>Math.abs(e-t);function k1(e,t){const n=Nc(e.x,t.x),r=Nc(e.y,t.y);return Math.sqrt(n**2+r**2)}class zh{constructor(t,n,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const c=As(this.lastMoveEventInfo,this.history),p=this.startEvent!==null,m=k1(c.offset,{x:0,y:0})>=3;if(!p&&!m)return;const{point:y}=c,{timestamp:x}=pe;this.history.push({...y,timestamp:x});const{onStart:C,onMove:g}=this.handlers;p||(C&&C(this.lastMoveEvent,c),this.startEvent=this.lastMoveEvent),g&&g(this.lastMoveEvent,c)},this.handlePointerMove=(c,p)=>{this.lastMoveEvent=c,this.lastMoveEventInfo=Ls(p,this.transformPagePoint),B.update(this.updatePoint,!0)},this.handlePointerUp=(c,p)=>{this.end();const{onEnd:m,onSessionEnd:y,resumeAnimation:x}=this.handlers;if(this.dragSnapToOrigin&&x&&x(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const C=As(c.type==="pointercancel"?this.lastMoveEventInfo:Ls(p,this.transformPagePoint),this.history);this.startEvent&&m&&m(c,C),y&&y(c,C)},!nh(t))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=r,this.contextWindow=i||window;const s=Wo(t),l=Ls(s,this.transformPagePoint),{point:a}=l,{timestamp:u}=pe;this.history=[{...a,timestamp:u}];const{onSessionStart:f}=n;f&&f(t,As(l,this.history)),this.removeListeners=zt(ft(this.contextWindow,"pointermove",this.handlePointerMove),ft(this.contextWindow,"pointerup",this.handlePointerUp),ft(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),yt(this.updatePoint)}}function Ls(e,t){return t?{point:t(e.point)}:e}function jc(e,t){return{x:e.x-t.x,y:e.y-t.y}}function As({point:e},t){return{point:e,delta:jc(e,Bh(t)),offset:jc(e,C1(t)),velocity:P1(t,.1)}}function C1(e){return e[0]}function Bh(e){return e[e.length-1]}function P1(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=Bh(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>fn(t)));)n--;if(!r)return{x:0,y:0};const o=dt(i.timestamp-r.timestamp);if(o===0)return{x:0,y:0};const s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}function _e(e){return e.max-e.min}function Il(e,t=0,n=.01){return Math.abs(e-t)<=n}function Oc(e,t,n,r=.5){e.origin=r,e.originPoint=Q(t.min,t.max,e.origin),e.scale=_e(n)/_e(t),(Il(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=Q(n.min,n.max,e.origin)-e.originPoint,(Il(e.translate)||isNaN(e.translate))&&(e.translate=0)}function Ar(e,t,n,r){Oc(e.x,t.x,n.x,r?r.originX:void 0),Oc(e.y,t.y,n.y,r?r.originY:void 0)}function Ic(e,t,n){e.min=n.min+t.min,e.max=e.min+_e(t)}function T1(e,t,n){Ic(e.x,t.x,n.x),Ic(e.y,t.y,n.y)}function Fc(e,t,n){e.min=t.min-n.min,e.max=e.min+_e(t)}function Rr(e,t,n){Fc(e.x,t.x,n.x),Fc(e.y,t.y,n.y)}function E1(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?Q(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?Q(n,e,r.max):Math.min(e,n)),e}function zc(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function D1(e,{top:t,left:n,bottom:r,right:i}){return{x:zc(e.x,n,i),y:zc(e.y,t,r)}}function Bc(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function L1(e,t){return{x:Bc(e.x,t.x),y:Bc(e.y,t.y)}}function A1(e,t){let n=.5;const r=_e(e),i=_e(t);return i>r?n=Jr(t.min,t.max-r,e.min):r>i&&(n=Jr(e.min,e.max-i,t.min)),$t(0,1,n)}function R1(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const Fl=.35;function V1(e=Fl){return e===!1?e=0:e===!0&&(e=Fl),{x:Uc(e,"left","right"),y:Uc(e,"top","bottom")}}function Uc(e,t,n){return{min:$c(e,t),max:$c(e,n)}}function $c(e,t){return typeof e=="number"?e:e[t]||0}const Hc=()=>({translate:0,scale:1,origin:0,originPoint:0}),In=()=>({x:Hc(),y:Hc()}),Wc=()=>({min:0,max:0}),te=()=>({x:Wc(),y:Wc()});function Fe(e){return[e("x"),e("y")]}function Uh({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function M1({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function _1(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function Rs(e){return e===void 0||e===1}function zl({scale:e,scaleX:t,scaleY:n}){return!Rs(e)||!Rs(t)||!Rs(n)}function en(e){return zl(e)||$h(e)||e.z||e.rotate||e.rotateX||e.rotateY}function $h(e){return Kc(e.x)||Kc(e.y)}function Kc(e){return e&&e!=="0%"}function wo(e,t,n){const r=e-n,i=t*r;return n+i}function Gc(e,t,n,r,i){return i!==void 0&&(e=wo(e,i,r)),wo(e,n,r)+t}function Bl(e,t=0,n=1,r,i){e.min=Gc(e.min,t,n,r,i),e.max=Gc(e.max,t,n,r,i)}function Hh(e,{x:t,y:n}){Bl(e.x,t.translate,t.scale,t.originPoint),Bl(e.y,n.translate,n.scale,n.originPoint)}function N1(e,t,n,r=!1){const i=n.length;if(!i)return;t.x=t.y=1;let o,s;for(let l=0;l<i;l++){o=n[l],s=o.projectionDelta;const a=o.instance;a&&a.style&&a.style.display==="contents"||(r&&o.options.layoutScroll&&o.scroll&&o!==o.root&&Fn(e,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,Hh(e,s)),r&&en(o.latestValues)&&Fn(e,o.latestValues))}t.x=Qc(t.x),t.y=Qc(t.y)}function Qc(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function Pt(e,t){e.min=e.min+t,e.max=e.max+t}function Yc(e,t,[n,r,i]){const o=t[i]!==void 0?t[i]:.5,s=Q(e.min,e.max,o);Bl(e,t[n],t[r],s,t.scale)}const j1=["x","scaleX","originX"],O1=["y","scaleY","originY"];function Fn(e,t){Yc(e.x,t,j1),Yc(e.y,t,O1)}function Wh(e,t){return Uh(_1(e.getBoundingClientRect(),t))}function I1(e,t,n){const r=Wh(e,n),{scroll:i}=t;return i&&(Pt(r.x,i.offset.x),Pt(r.y,i.offset.y)),r}const Kh=({current:e})=>e?e.ownerDocument.defaultView:null,F1=new WeakMap;class z1{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=te(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const i=f=>{const{dragSnapToOrigin:c}=this.getProps();c?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(Wo(f,"page").point)},o=(f,c)=>{const{drag:p,dragPropagation:m,onDragStart:y}=this.getProps();if(p&&!m&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=ih(p),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Fe(C=>{let g=this.getAxisMotionValue(C).get()||0;if(it.test(g)){const{projection:d}=this.visualElement;if(d&&d.layout){const h=d.layout.layoutBox[C];h&&(g=_e(h)*(parseFloat(g)/100))}}this.originPoint[C]=g}),y&&B.update(()=>y(f,c),!1,!0);const{animationState:x}=this.visualElement;x&&x.setActive("whileDrag",!0)},s=(f,c)=>{const{dragPropagation:p,dragDirectionLock:m,onDirectionLock:y,onDrag:x}=this.getProps();if(!p&&!this.openGlobalLock)return;const{offset:C}=c;if(m&&this.currentDirection===null){this.currentDirection=B1(C),this.currentDirection!==null&&y&&y(this.currentDirection);return}this.updateAxis("x",c.point,C),this.updateAxis("y",c.point,C),this.visualElement.render(),x&&x(f,c)},l=(f,c)=>this.stop(f,c),a=()=>Fe(f=>{var c;return this.getAnimationState(f)==="paused"&&((c=this.getAxisMotionValue(f).animation)===null||c===void 0?void 0:c.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new zh(t,{onSessionStart:i,onStart:o,onMove:s,onSessionEnd:l,resumeAnimation:a},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:Kh(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:o}=this.getProps();o&&B.update(()=>o(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:i}=this.getProps();if(!r||!Li(t,i,this.currentDirection))return;const o=this.getAxisMotionValue(t);let s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=E1(s,this.constraints[t],this.elastic[t])),o.set(s)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,o=this.constraints;n&&jn(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=D1(i.layoutBox,n):this.constraints=!1,this.elastic=V1(r),o!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&Fe(s=>{this.getAxisMotionValue(s)&&(this.constraints[s]=R1(i.layoutBox[s],this.constraints[s]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!jn(t))return!1;const r=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=I1(r,i.root,this.visualElement.getTransformPagePoint());let s=L1(i.layout.layoutBox,o);if(n){const l=n(M1(s));this.hasMutatedConstraints=!!l,l&&(s=Uh(l))}return s}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:o,dragSnapToOrigin:s,onDragTransitionEnd:l}=this.getProps(),a=this.constraints||{},u=Fe(f=>{if(!Li(f,n,this.currentDirection))return;let c=a&&a[f]||{};s&&(c={min:0,max:0});const p=i?200:1e6,m=i?40:1e7,y={type:"inertia",velocity:r?t[f]:0,bounceStiffness:p,bounceDamping:m,timeConstant:750,restDelta:1,restSpeed:10,...o,...c};return this.startAxisValueAnimation(f,y)});return Promise.all(u).then(l)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return r.start(ru(t,r,0,n))}stopAnimation(){Fe(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Fe(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n="_drag"+t.toUpperCase(),r=this.visualElement.getProps(),i=r[n];return i||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){Fe(n=>{const{drag:r}=this.getProps();if(!Li(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:s,max:l}=i.layout.layoutBox[n];o.set(t[n]-Q(s,l,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!jn(n)||!r||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Fe(s=>{const l=this.getAxisMotionValue(s);if(l){const a=l.get();i[s]=A1({min:a,max:a},this.constraints[s])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Fe(s=>{if(!Li(s,t,null))return;const l=this.getAxisMotionValue(s),{min:a,max:u}=this.constraints[s];l.set(Q(a,u,i[s]))})}addListeners(){if(!this.visualElement.current)return;F1.set(this.visualElement,this);const t=this.visualElement.current,n=ft(t,"pointerdown",a=>{const{drag:u,dragListener:f=!0}=this.getProps();u&&f&&this.start(a)}),r=()=>{const{dragConstraints:a}=this.getProps();jn(a)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,o=i.addEventListener("measure",r);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),r();const s=ut(window,"resize",()=>this.scalePositionWithinConstraints()),l=i.addEventListener("didUpdate",({delta:a,hasLayoutChanged:u})=>{this.isDragging&&u&&(Fe(f=>{const c=this.getAxisMotionValue(f);c&&(this.originPoint[f]+=a[f].translate,c.set(c.get()+a[f].translate))}),this.visualElement.render())});return()=>{s(),n(),o(),l&&l()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:s=Fl,dragMomentum:l=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:o,dragElastic:s,dragMomentum:l}}}function Li(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function B1(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class U1 extends Qt{constructor(t){super(t),this.removeGroupControls=ee,this.removeListeners=ee,this.controls=new z1(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ee}unmount(){this.removeGroupControls(),this.removeListeners()}}const Xc=e=>(t,n)=>{e&&B.update(()=>e(t,n))};class $1 extends Qt{constructor(){super(...arguments),this.removePointerDownListener=ee}onPointerDown(t){this.session=new zh(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Kh(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:Xc(t),onStart:Xc(n),onMove:r,onEnd:(o,s)=>{delete this.session,i&&B.update(()=>i(o,s))}}}mount(){this.removePointerDownListener=ft(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}function H1(){const e=k.useContext(zo);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,i=k.useId();return k.useEffect(()=>r(i),[]),!t&&n?[!1,()=>n&&n(i)]:[!0]}const $i={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Zc(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const dr={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(M.test(e))e=parseFloat(e);else return e;const n=Zc(e,t.target.x),r=Zc(e,t.target.y);return`${n}% ${r}%`}},W1={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=Ht.parse(e);if(i.length>5)return r;const o=Ht.createTransformer(e),s=typeof i[0]!="number"?1:0,l=n.x.scale*t.x,a=n.y.scale*t.y;i[0+s]/=l,i[1+s]/=a;const u=Q(l,a,.5);return typeof i[2+s]=="number"&&(i[2+s]/=u),typeof i[3+s]=="number"&&(i[3+s]/=u),o(i)}};class K1 extends Vr.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:o}=t;qy(G1),o&&(n.group&&n.group.add(o),r&&r.register&&i&&r.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),$i.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:i,isPresent:o}=this.props,s=r.projection;return s&&(s.isPresent=o,i||t.layoutDependency!==n||n===void 0?s.willUpdate():this.safeToRemove(),t.isPresent!==o&&(o?s.promote():s.relegate()||B.postRender(()=>{const l=s.getStack();(!l||!l.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Gh(e){const[t,n]=H1(),r=k.useContext(Ha);return Vr.createElement(K1,{...e,layoutGroup:r,switchLayoutGroup:k.useContext(Up),isPresent:t,safeToRemove:n})}const G1={borderRadius:{...dr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:dr,borderTopRightRadius:dr,borderBottomLeftRadius:dr,borderBottomRightRadius:dr,boxShadow:W1},Qh=["TopLeft","TopRight","BottomLeft","BottomRight"],Q1=Qh.length,Jc=e=>typeof e=="string"?parseFloat(e):e,qc=e=>typeof e=="number"||M.test(e);function Y1(e,t,n,r,i,o){i?(e.opacity=Q(0,n.opacity!==void 0?n.opacity:1,X1(r)),e.opacityExit=Q(t.opacity!==void 0?t.opacity:1,0,Z1(r))):o&&(e.opacity=Q(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let s=0;s<Q1;s++){const l=`border${Qh[s]}Radius`;let a=bc(t,l),u=bc(n,l);if(a===void 0&&u===void 0)continue;a||(a=0),u||(u=0),a===0||u===0||qc(a)===qc(u)?(e[l]=Math.max(Q(Jc(a),Jc(u),r),0),(it.test(u)||it.test(a))&&(e[l]+="%")):e[l]=u}(t.rotate||n.rotate)&&(e.rotate=Q(t.rotate||0,n.rotate||0,r))}function bc(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const X1=Yh(0,.5,gh),Z1=Yh(.5,.95,ee);function Yh(e,t,n){return r=>r<e?0:r>t?1:n(Jr(e,t,r))}function ef(e,t){e.min=t.min,e.max=t.max}function Ie(e,t){ef(e.x,t.x),ef(e.y,t.y)}function tf(e,t,n,r,i){return e-=t,e=wo(e,1/n,r),i!==void 0&&(e=wo(e,1/i,r)),e}function J1(e,t=0,n=1,r=.5,i,o=e,s=e){if(it.test(t)&&(t=parseFloat(t),t=Q(s.min,s.max,t/100)-s.min),typeof t!="number")return;let l=Q(o.min,o.max,r);e===o&&(l-=t),e.min=tf(e.min,t,n,l,i),e.max=tf(e.max,t,n,l,i)}function nf(e,t,[n,r,i],o,s){J1(e,t[n],t[r],t[i],t.scale,o,s)}const q1=["x","scaleX","originX"],b1=["y","scaleY","originY"];function rf(e,t,n,r){nf(e.x,t,q1,n?n.x:void 0,r?r.x:void 0),nf(e.y,t,b1,n?n.y:void 0,r?r.y:void 0)}function of(e){return e.translate===0&&e.scale===1}function Xh(e){return of(e.x)&&of(e.y)}function ex(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function Zh(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function sf(e){return _e(e.x)/_e(e.y)}class tx{constructor(){this.members=[]}add(t){iu(this.members,t),t.scheduleRender()}remove(t){if(ou(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(i=>t===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){r=o;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;i===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function lf(e,t,n){let r="";const i=e.x.translate/t.x,o=e.y.translate/t.y;if((i||o)&&(r=`translate3d(${i}px, ${o}px, 0) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{rotate:a,rotateX:u,rotateY:f}=n;a&&(r+=`rotate(${a}deg) `),u&&(r+=`rotateX(${u}deg) `),f&&(r+=`rotateY(${f}deg) `)}const s=e.x.scale*t.x,l=e.y.scale*t.y;return(s!==1||l!==1)&&(r+=`scale(${s}, ${l})`),r||"none"}const nx=(e,t)=>e.depth-t.depth;class rx{constructor(){this.children=[],this.isDirty=!1}add(t){iu(this.children,t),this.isDirty=!0}remove(t){ou(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(nx),this.isDirty=!1,this.children.forEach(t)}}function ix(e,t){const n=performance.now(),r=({timestamp:i})=>{const o=i-n;o>=t&&(yt(r),e(o-t))};return B.read(r,!0),()=>yt(r)}function ox(e){window.MotionDebug&&window.MotionDebug.record(e)}function sx(e){return e instanceof SVGElement&&e.tagName!=="svg"}function lx(e,t,n){const r=De(e)?e:qn(e);return r.start(ru("",r,t,n)),r.animation}const af=["","X","Y","Z"],ax={visibility:"hidden"},uf=1e3;let ux=0;const tn={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function Jh({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(s={},l=t==null?void 0:t()){this.id=ux++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,tn.totalNodes=tn.resolvedTargetDeltas=tn.recalculatedProjection=0,this.nodes.forEach(dx),this.nodes.forEach(yx),this.nodes.forEach(vx),this.nodes.forEach(px),ox(tn)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=s,this.root=l?l.root||l:this,this.path=l?[...l.path,l]:[],this.parent=l,this.depth=l?l.depth+1:0;for(let a=0;a<this.path.length;a++)this.path[a].shouldResetTransform=!0;this.root===this&&(this.nodes=new rx)}addEventListener(s,l){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new su),this.eventHandlers.get(s).add(l)}notifyListeners(s,...l){const a=this.eventHandlers.get(s);a&&a.notify(...l)}hasListeners(s){return this.eventHandlers.has(s)}mount(s,l=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=sx(s),this.instance=s;const{layoutId:a,layout:u,visualElement:f}=this.options;if(f&&!f.current&&f.mount(s),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),l&&(u||a)&&(this.isLayoutDirty=!0),e){let c;const p=()=>this.root.updateBlockedByResize=!1;e(s,()=>{this.root.updateBlockedByResize=!0,c&&c(),c=ix(p,250),$i.hasAnimatedSinceResize&&($i.hasAnimatedSinceResize=!1,this.nodes.forEach(ff))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&f&&(a||u)&&this.addEventListener("didUpdate",({delta:c,hasLayoutChanged:p,hasRelativeTargetChanged:m,layout:y})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const x=this.options.transition||f.getDefaultTransition()||Cx,{onLayoutAnimationStart:C,onLayoutAnimationComplete:g}=f.getProps(),d=!this.targetLayout||!Zh(this.targetLayout,y)||m,h=!p&&m;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||h||p&&(d||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(c,h);const v={...nu(x,"layout"),onPlay:C,onComplete:g};(f.shouldReduceMotion||this.options.layoutRoot)&&(v.delay=0,v.type=!1),this.startAnimation(v)}else p||ff(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=y})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const s=this.getStack();s&&s.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,yt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(xx),this.animationId++)}getTransformTemplate(){const{visualElement:s}=this.options;return s&&s.getProps().transformTemplate}willUpdate(s=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let f=0;f<this.path.length;f++){const c=this.path[f];c.shouldResetTransform=!0,c.updateScroll("snapshot"),c.options.layoutRoot&&c.willUpdate(!1)}const{layoutId:l,layout:a}=this.options;if(l===void 0&&!a)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(cf);return}this.isUpdating||this.nodes.forEach(mx),this.isUpdating=!1,this.nodes.forEach(gx),this.nodes.forEach(cx),this.nodes.forEach(fx),this.clearAllSnapshots();const l=performance.now();pe.delta=$t(0,1e3/60,l-pe.timestamp),pe.timestamp=l,pe.isProcessing=!0,Ss.update.process(pe),Ss.preRender.process(pe),Ss.render.process(pe),pe.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(hx),this.sharedNodes.forEach(Sx)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,B.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){B.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();const s=this.layout;this.layout=this.measure(!1),this.layoutCorrected=te(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:l}=this.options;l&&l.notify("LayoutMeasure",this.layout.layoutBox,s?s.layoutBox:void 0)}updateScroll(s="measure"){let l=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(l=!1),l&&(this.scroll={animationId:this.root.animationId,phase:s,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){if(!i)return;const s=this.isLayoutDirty||this.shouldResetTransform,l=this.projectionDelta&&!Xh(this.projectionDelta),a=this.getTransformTemplate(),u=a?a(this.latestValues,""):void 0,f=u!==this.prevTransformTemplateValue;s&&(l||en(this.latestValues)||f)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const l=this.measurePageBox();let a=this.removeElementScroll(l);return s&&(a=this.removeTransform(a)),Px(a),{animationId:this.root.animationId,measuredBox:l,layoutBox:a,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:s}=this.options;if(!s)return te();const l=s.measureViewportBox(),{scroll:a}=this.root;return a&&(Pt(l.x,a.offset.x),Pt(l.y,a.offset.y)),l}removeElementScroll(s){const l=te();Ie(l,s);for(let a=0;a<this.path.length;a++){const u=this.path[a],{scroll:f,options:c}=u;if(u!==this.root&&f&&c.layoutScroll){if(f.isRoot){Ie(l,s);const{scroll:p}=this.root;p&&(Pt(l.x,-p.offset.x),Pt(l.y,-p.offset.y))}Pt(l.x,f.offset.x),Pt(l.y,f.offset.y)}}return l}applyTransform(s,l=!1){const a=te();Ie(a,s);for(let u=0;u<this.path.length;u++){const f=this.path[u];!l&&f.options.layoutScroll&&f.scroll&&f!==f.root&&Fn(a,{x:-f.scroll.offset.x,y:-f.scroll.offset.y}),en(f.latestValues)&&Fn(a,f.latestValues)}return en(this.latestValues)&&Fn(a,this.latestValues),a}removeTransform(s){const l=te();Ie(l,s);for(let a=0;a<this.path.length;a++){const u=this.path[a];if(!u.instance||!en(u.latestValues))continue;zl(u.latestValues)&&u.updateSnapshot();const f=te(),c=u.measurePageBox();Ie(f,c),rf(l,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,f)}return en(this.latestValues)&&rf(l,this.latestValues),l}setTargetDelta(s){this.targetDelta=s,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==pe.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(s=!1){var l;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==a;if(!(s||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((l=this.parent)===null||l===void 0)&&l.isProjectionDirty||this.attemptToResolveRelativeTarget))return;const{layout:c,layoutId:p}=this.options;if(!(!this.layout||!(c||p))){if(this.resolvedRelativeTargetAt=pe.timestamp,!this.targetDelta&&!this.relativeTarget){const m=this.getClosestProjectingParent();m&&m.layout&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=te(),this.relativeTargetOrigin=te(),Rr(this.relativeTargetOrigin,this.layout.layoutBox,m.layout.layoutBox),Ie(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=te(),this.targetWithTransforms=te()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),T1(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Ie(this.target,this.layout.layoutBox),Hh(this.target,this.targetDelta)):Ie(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const m=this.getClosestProjectingParent();m&&!!m.resumingFrom==!!this.resumingFrom&&!m.options.layoutScroll&&m.target&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=te(),this.relativeTargetOrigin=te(),Rr(this.relativeTargetOrigin,this.target,m.target),Ie(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}tn.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||zl(this.parent.latestValues)||$h(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var s;const l=this.getLead(),a=!!this.resumingFrom||this!==l;let u=!0;if((this.isProjectionDirty||!((s=this.parent)===null||s===void 0)&&s.isProjectionDirty)&&(u=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===pe.timestamp&&(u=!1),u)return;const{layout:f,layoutId:c}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(f||c))return;Ie(this.layoutCorrected,this.layout.layoutBox);const p=this.treeScale.x,m=this.treeScale.y;N1(this.layoutCorrected,this.treeScale,this.path,a),l.layout&&!l.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(l.target=l.layout.layoutBox);const{target:y}=l;if(!y){this.projectionTransform&&(this.projectionDelta=In(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=In(),this.projectionDeltaWithTransform=In());const x=this.projectionTransform;Ar(this.projectionDelta,this.layoutCorrected,y,this.latestValues),this.projectionTransform=lf(this.projectionDelta,this.treeScale),(this.projectionTransform!==x||this.treeScale.x!==p||this.treeScale.y!==m)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",y)),tn.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),s){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(s,l=!1){const a=this.snapshot,u=a?a.latestValues:{},f={...this.latestValues},c=In();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!l;const p=te(),m=a?a.source:void 0,y=this.layout?this.layout.source:void 0,x=m!==y,C=this.getStack(),g=!C||C.members.length<=1,d=!!(x&&!g&&this.options.crossfade===!0&&!this.path.some(kx));this.animationProgress=0;let h;this.mixTargetDelta=v=>{const S=v/1e3;df(c.x,s.x,S),df(c.y,s.y,S),this.setTargetDelta(c),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Rr(p,this.layout.layoutBox,this.relativeParent.layout.layoutBox),wx(this.relativeTarget,this.relativeTargetOrigin,p,S),h&&ex(this.relativeTarget,h)&&(this.isProjectionDirty=!1),h||(h=te()),Ie(h,this.relativeTarget)),x&&(this.animationValues=f,Y1(f,u,this.latestValues,S,d,g)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=S},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(s){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(yt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=B.update(()=>{$i.hasAnimatedSinceResize=!0,this.currentAnimation=lx(0,uf,{...s,onUpdate:l=>{this.mixTargetDelta(l),s.onUpdate&&s.onUpdate(l)},onComplete:()=>{s.onComplete&&s.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const s=this.getStack();s&&s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(uf),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:l,target:a,layout:u,latestValues:f}=s;if(!(!l||!a||!u)){if(this!==s&&this.layout&&u&&qh(this.options.animationType,this.layout.layoutBox,u.layoutBox)){a=this.target||te();const c=_e(this.layout.layoutBox.x);a.x.min=s.target.x.min,a.x.max=a.x.min+c;const p=_e(this.layout.layoutBox.y);a.y.min=s.target.y.min,a.y.max=a.y.min+p}Ie(l,a),Fn(l,f),Ar(this.projectionDeltaWithTransform,this.layoutCorrected,l,f)}}registerSharedNode(s,l){this.sharedNodes.has(s)||this.sharedNodes.set(s,new tx),this.sharedNodes.get(s).add(l);const u=l.options.initialPromotionConfig;l.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(l):void 0})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:l}=this.options;return l?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:l}=this.options;return l?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:l,preserveFollowOpacity:a}={}){const u=this.getStack();u&&u.promote(this,a),s&&(this.projectionDelta=void 0,this.needsReset=!0),l&&this.setOptions({transition:l})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetRotation(){const{visualElement:s}=this.options;if(!s)return;let l=!1;const{latestValues:a}=s;if((a.rotate||a.rotateX||a.rotateY||a.rotateZ)&&(l=!0),!l)return;const u={};for(let f=0;f<af.length;f++){const c="rotate"+af[f];a[c]&&(u[c]=a[c],s.setStaticValue(c,0))}s.render();for(const f in u)s.setStaticValue(f,u[f]);s.scheduleRender()}getProjectionStyles(s){var l,a;if(!this.instance||this.isSVG)return;if(!this.isVisible)return ax;const u={visibility:""},f=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=Ui(s==null?void 0:s.pointerEvents)||"",u.transform=f?f(this.latestValues,""):"none",u;const c=this.getLead();if(!this.projectionDelta||!this.layout||!c.target){const x={};return this.options.layoutId&&(x.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,x.pointerEvents=Ui(s==null?void 0:s.pointerEvents)||""),this.hasProjected&&!en(this.latestValues)&&(x.transform=f?f({},""):"none",this.hasProjected=!1),x}const p=c.animationValues||c.latestValues;this.applyTransformsToTarget(),u.transform=lf(this.projectionDeltaWithTransform,this.treeScale,p),f&&(u.transform=f(p,u.transform));const{x:m,y}=this.projectionDelta;u.transformOrigin=`${m.origin*100}% ${y.origin*100}% 0`,c.animationValues?u.opacity=c===this?(a=(l=p.opacity)!==null&&l!==void 0?l:this.latestValues.opacity)!==null&&a!==void 0?a:1:this.preserveOpacity?this.latestValues.opacity:p.opacityExit:u.opacity=c===this?p.opacity!==void 0?p.opacity:"":p.opacityExit!==void 0?p.opacityExit:0;for(const x in ho){if(p[x]===void 0)continue;const{correct:C,applyTo:g}=ho[x],d=u.transform==="none"?p[x]:C(p[x],c);if(g){const h=g.length;for(let v=0;v<h;v++)u[g[v]]=d}else u[x]=d}return this.options.layoutId&&(u.pointerEvents=c===this?Ui(s==null?void 0:s.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var l;return(l=s.currentAnimation)===null||l===void 0?void 0:l.stop()}),this.root.nodes.forEach(cf),this.root.sharedNodes.clear()}}}function cx(e){e.updateLayout()}function fx(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:i}=e.layout,{animationType:o}=e.options,s=n.source!==e.layout.source;o==="size"?Fe(c=>{const p=s?n.measuredBox[c]:n.layoutBox[c],m=_e(p);p.min=r[c].min,p.max=p.min+m}):qh(o,n.layoutBox,r)&&Fe(c=>{const p=s?n.measuredBox[c]:n.layoutBox[c],m=_e(r[c]);p.max=p.min+m,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[c].max=e.relativeTarget[c].min+m)});const l=In();Ar(l,r,n.layoutBox);const a=In();s?Ar(a,e.applyTransform(i,!0),n.measuredBox):Ar(a,r,n.layoutBox);const u=!Xh(l);let f=!1;if(!e.resumeFrom){const c=e.getClosestProjectingParent();if(c&&!c.resumeFrom){const{snapshot:p,layout:m}=c;if(p&&m){const y=te();Rr(y,n.layoutBox,p.layoutBox);const x=te();Rr(x,r,m.layoutBox),Zh(y,x)||(f=!0),c.options.layoutRoot&&(e.relativeTarget=x,e.relativeTargetOrigin=y,e.relativeParent=c)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:a,layoutDelta:l,hasLayoutChanged:u,hasRelativeTargetChanged:f})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function dx(e){tn.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function px(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function hx(e){e.clearSnapshot()}function cf(e){e.clearMeasurements()}function mx(e){e.isLayoutDirty=!1}function gx(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function ff(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function yx(e){e.resolveTargetDelta()}function vx(e){e.calcProjection()}function xx(e){e.resetRotation()}function Sx(e){e.removeLeadSnapshot()}function df(e,t,n){e.translate=Q(t.translate,0,n),e.scale=Q(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function pf(e,t,n,r){e.min=Q(t.min,n.min,r),e.max=Q(t.max,n.max,r)}function wx(e,t,n,r){pf(e.x,t.x,n.x,r),pf(e.y,t.y,n.y,r)}function kx(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const Cx={duration:.45,ease:[.4,0,.1,1]},hf=e=>typeof navigator<"u"&&navigator.userAgent.toLowerCase().includes(e),mf=hf("applewebkit/")&&!hf("chrome/")?Math.round:ee;function gf(e){e.min=mf(e.min),e.max=mf(e.max)}function Px(e){gf(e.x),gf(e.y)}function qh(e,t,n){return e==="position"||e==="preserve-aspect"&&!Il(sf(t),sf(n),.2)}const Tx=Jh({attachResizeListener:(e,t)=>ut(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Vs={current:void 0},bh=Jh({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Vs.current){const e=new Tx({});e.mount(window),e.setOptions({layoutScroll:!0}),Vs.current=e}return Vs.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),Ex={pan:{Feature:$1},drag:{Feature:U1,ProjectionNode:bh,MeasureLayout:Gh}},Dx=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function Lx(e){const t=Dx.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}function Ul(e,t,n=1){const[r,i]=Lx(e);if(!r)return;const o=window.getComputedStyle(t).getPropertyValue(r);if(o){const s=o.trim();return jh(s)?parseFloat(s):s}else return Rl(i)?Ul(i,t,n+1):i}function Ax(e,{...t},n){const r=e.current;if(!(r instanceof Element))return{target:t,transitionEnd:n};n&&(n={...n}),e.values.forEach(i=>{const o=i.get();if(!Rl(o))return;const s=Ul(o,r);s&&i.set(s)});for(const i in t){const o=t[i];if(!Rl(o))continue;const s=Ul(o,r);s&&(t[i]=s,n||(n={}),n[i]===void 0&&(n[i]=o))}return{target:t,transitionEnd:n}}const Rx=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),em=e=>Rx.has(e),Vx=e=>Object.keys(e).some(em),yf=e=>e===Sn||e===M,vf=(e,t)=>parseFloat(e.split(", ")[t]),xf=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/);if(i)return vf(i[1],t);{const o=r.match(/^matrix\((.+)\)$/);return o?vf(o[1],e):0}},Mx=new Set(["x","y","z"]),_x=ri.filter(e=>!Mx.has(e));function Nx(e){const t=[];return _x.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}const bn={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:xf(4,13),y:xf(5,14)};bn.translateX=bn.x;bn.translateY=bn.y;const jx=(e,t,n)=>{const r=t.measureViewportBox(),i=t.current,o=getComputedStyle(i),{display:s}=o,l={};s==="none"&&t.setStaticValue("display",e.display||"block"),n.forEach(u=>{l[u]=bn[u](r,o)}),t.render();const a=t.measureViewportBox();return n.forEach(u=>{const f=t.getValue(u);f&&f.jump(l[u]),e[u]=bn[u](a,o)}),e},Ox=(e,t,n={},r={})=>{t={...t},r={...r};const i=Object.keys(t).filter(em);let o=[],s=!1;const l=[];if(i.forEach(a=>{const u=e.getValue(a);if(!e.hasValue(a))return;let f=n[a],c=fr(f);const p=t[a];let m;if(go(p)){const y=p.length,x=p[0]===null?1:0;f=p[x],c=fr(f);for(let C=x;C<y&&p[C]!==null;C++)m?Ja(fr(p[C])===m):m=fr(p[C])}else m=fr(p);if(c!==m)if(yf(c)&&yf(m)){const y=u.get();typeof y=="string"&&u.set(parseFloat(y)),typeof p=="string"?t[a]=parseFloat(p):Array.isArray(p)&&m===M&&(t[a]=p.map(parseFloat))}else c!=null&&c.transform&&(m!=null&&m.transform)&&(f===0||p===0)?f===0?u.set(m.transform(f)):t[a]=c.transform(p):(s||(o=Nx(e),s=!0),l.push(a),r[a]=r[a]!==void 0?r[a]:t[a],u.jump(p))}),l.length){const a=l.indexOf("height")>=0?window.pageYOffset:null,u=jx(t,e,l);return o.length&&o.forEach(([f,c])=>{e.getValue(f).set(c)}),e.render(),Bo&&a!==null&&window.scrollTo({top:a}),{target:u,transitionEnd:r}}else return{target:t,transitionEnd:r}};function Ix(e,t,n,r){return Vx(t)?Ox(e,t,n,r):{target:t,transitionEnd:r}}const Fx=(e,t,n,r)=>{const i=Ax(e,t,r);return t=i.target,r=i.transitionEnd,Ix(e,t,n,r)},$l={current:null},tm={current:!1};function zx(){if(tm.current=!0,!!Bo)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>$l.current=e.matches;e.addListener(t),t()}else $l.current=!1}function Bx(e,t,n){const{willChange:r}=t;for(const i in t){const o=t[i],s=n[i];if(De(o))e.addValue(i,o),So(r)&&r.add(i);else if(De(s))e.addValue(i,qn(o,{owner:e})),So(r)&&r.remove(i);else if(s!==o)if(e.hasValue(i)){const l=e.getValue(i);!l.hasAnimated&&l.set(o)}else{const l=e.getStaticValue(i);e.addValue(i,qn(l!==void 0?l:o,{owner:e}))}}for(const i in n)t[i]===void 0&&e.removeValue(i);return t}const Sf=new WeakMap,nm=Object.keys(Zr),Ux=nm.length,wf=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],$x=$a.length;class Hx{constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:i,visualState:o},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>B.render(this.render,!1,!0);const{latestValues:l,renderState:a}=o;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=a,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=s,this.isControllingVariants=$o(n),this.isVariantNode=Bp(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:u,...f}=this.scrapeMotionValuesFromProps(n,{});for(const c in f){const p=f[c];l[c]!==void 0&&De(p)&&(p.set(l[c],!1),So(u)&&u.add(c))}}scrapeMotionValuesFromProps(t,n){return{}}mount(t){this.current=t,Sf.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),tm.current||zx(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:$l.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Sf.delete(this.current),this.projection&&this.projection.unmount(),yt(this.notifyUpdate),yt(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,n){const r=xn.has(t),i=n.on("change",s=>{this.latestValues[t]=s,this.props.onUpdate&&B.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),o()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}loadFeatures({children:t,...n},r,i,o){let s,l;for(let a=0;a<Ux;a++){const u=nm[a],{isEnabled:f,Feature:c,ProjectionNode:p,MeasureLayout:m}=Zr[u];p&&(s=p),f(n)&&(!this.features[u]&&c&&(this.features[u]=new c(this)),m&&(l=m))}if((this.type==="html"||this.type==="svg")&&!this.projection&&s){this.projection=new s(this.latestValues,this.parent&&this.parent.projection);const{layoutId:a,layout:u,drag:f,dragConstraints:c,layoutScroll:p,layoutRoot:m}=n;this.projection.setOptions({layoutId:a,layout:u,alwaysMeasureLayout:!!f||c&&jn(c),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof u=="string"?u:"both",initialPromotionConfig:o,layoutScroll:p,layoutRoot:m})}return l}updateFeatures(){for(const t in this.features){const n=this.features[t];n.isMounted?n.update():(n.mount(),n.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):te()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}makeTargetAnimatable(t,n=!0){return this.makeTargetAnimatableFromInstance(t,this.props,n)}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<wf.length;r++){const i=wf[r];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const o=t["on"+i];o&&(this.propEventSubscriptions[i]=this.on(i,o))}this.prevMotionValues=Bx(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const r=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}const n={};for(let r=0;r<$x;r++){const i=$a[r],o=this.props[i];(Xr(o)||o===!1)&&(n[i]=o)}return n}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){n!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,n)),this.values.set(t,n),this.latestValues[t]=n.get()}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=qn(n,{owner:this}),this.addValue(t,r)),r}readValue(t){var n;return this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(n=this.getBaseTargetFromProps(this.props,t))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,t,this.options)}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props,i=typeof r=="string"||typeof r=="object"?(n=Za(this.props,r))===null||n===void 0?void 0:n[t]:void 0;if(r&&i!==void 0)return i;const o=this.getBaseTargetFromProps(this.props,t);return o!==void 0&&!De(o)?o:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new su),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class rm extends Hx{sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:n,...r},{transformValues:i},o){let s=s1(r,t||{},this);if(i&&(n&&(n=i(n)),r&&(r=i(r)),s&&(s=i(s))),o){i1(this,r,s);const l=Fx(this,r,s,n);n=l.transitionEnd,r=l.target}return{transition:t,transitionEnd:n,...r}}}function Wx(e){return window.getComputedStyle(e)}class Kx extends rm{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,n){if(xn.has(n)){const r=tu(n);return r&&r.default||0}else{const r=Wx(t),i=(Wp(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return Wh(t,n)}build(t,n,r,i){Ka(t,n,r,i.transformTemplate)}scrapeMotionValuesFromProps(t,n){return Xa(t,n)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;De(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}renderInstance(t,n,r,i){Zp(t,n,r,i)}}class Gx extends rm{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(xn.has(n)){const r=tu(n);return r&&r.default||0}return n=Jp.has(n)?n:Ba(n),t.getAttribute(n)}measureInstanceViewportBox(){return te()}scrapeMotionValuesFromProps(t,n){return bp(t,n)}build(t,n,r,i){Qa(t,n,r,this.isSVGTag,i.transformTemplate)}renderInstance(t,n,r,i){qp(t,n,r,i)}mount(t){this.isSVGTag=Ya(t.tagName),super.mount(t)}}const Qx=(e,t)=>Wa(e)?new Gx(t,{enableHardwareAcceleration:!1}):new Kx(t,{enableHardwareAcceleration:!0}),Yx={layout:{ProjectionNode:bh,MeasureLayout:Gh}},Xx={...w1,...Uv,...Ex,...Yx},Hl=Zy((e,t)=>Lv(e,t,Xx,Qx));function im(){const e=k.useRef(!1);return za(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function Zx(){const e=im(),[t,n]=k.useState(0),r=k.useCallback(()=>{e.current&&n(t+1)},[t]);return[k.useCallback(()=>B.postRender(r),[r]),t]}class Jx extends k.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function qx({children:e,isPresent:t}){const n=k.useId(),r=k.useRef(null),i=k.useRef({width:0,height:0,top:0,left:0});return k.useInsertionEffect(()=>{const{width:o,height:s,top:l,left:a}=i.current;if(t||!r.current||!o||!s)return;r.current.dataset.motionPopId=n;const u=document.createElement("style");return document.head.appendChild(u),u.sheet&&u.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${o}px !important;
            height: ${s}px !important;
            top: ${l}px !important;
            left: ${a}px !important;
          }
        `),()=>{document.head.removeChild(u)}},[t]),k.createElement(Jx,{isPresent:t,childRef:r,sizeRef:i},k.cloneElement(e,{ref:r}))}const Ms=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:i,presenceAffectsLayout:o,mode:s})=>{const l=eh(bx),a=k.useId(),u=k.useMemo(()=>({id:a,initial:t,isPresent:n,custom:i,onExitComplete:f=>{l.set(f,!0);for(const c of l.values())if(!c)return;r&&r()},register:f=>(l.set(f,!1),()=>l.delete(f))}),o?void 0:[n]);return k.useMemo(()=>{l.forEach((f,c)=>l.set(c,!1))},[n]),k.useEffect(()=>{!n&&!l.size&&r&&r()},[n]),s==="popLayout"&&(e=k.createElement(qx,{isPresent:n},e)),k.createElement(zo.Provider,{value:u},e)};function bx(){return new Map}function eS(e){return k.useEffect(()=>()=>e(),[])}const nn=e=>e.key||"";function tS(e,t){e.forEach(n=>{const r=nn(n);t.set(r,n)})}function nS(e){const t=[];return k.Children.forEach(e,n=>{k.isValidElement(n)&&t.push(n)}),t}const kf=({children:e,custom:t,initial:n=!0,onExitComplete:r,exitBeforeEnter:i,presenceAffectsLayout:o=!0,mode:s="sync"})=>{const l=k.useContext(Ha).forceRender||Zx()[0],a=im(),u=nS(e);let f=u;const c=k.useRef(new Map).current,p=k.useRef(f),m=k.useRef(new Map).current,y=k.useRef(!0);if(za(()=>{y.current=!1,tS(u,m),p.current=f}),eS(()=>{y.current=!0,m.clear(),c.clear()}),y.current)return k.createElement(k.Fragment,null,f.map(d=>k.createElement(Ms,{key:nn(d),isPresent:!0,initial:n?void 0:!1,presenceAffectsLayout:o,mode:s},d)));f=[...f];const x=p.current.map(nn),C=u.map(nn),g=x.length;for(let d=0;d<g;d++){const h=x[d];C.indexOf(h)===-1&&!c.has(h)&&c.set(h,void 0)}return s==="wait"&&c.size&&(f=[]),c.forEach((d,h)=>{if(C.indexOf(h)!==-1)return;const v=m.get(h);if(!v)return;const S=x.indexOf(h);let w=d;if(!w){const E=()=>{c.delete(h);const P=Array.from(m.keys()).filter(R=>!C.includes(R));if(P.forEach(R=>m.delete(R)),p.current=u.filter(R=>{const V=nn(R);return V===h||P.includes(V)}),!c.size){if(a.current===!1)return;l(),r&&r()}};w=k.createElement(Ms,{key:nn(v),isPresent:!1,onExitComplete:E,custom:t,presenceAffectsLayout:o,mode:s},v),c.set(h,w)}f.splice(S,0,w)}),f=f.map(d=>{const h=d.key;return c.has(h)?d:k.createElement(Ms,{key:nn(d),isPresent:!0,presenceAffectsLayout:o,mode:s},d)}),k.createElement(k.Fragment,null,c.size?f:f.map(d=>k.cloneElement(d)))};function Wl(){return Wl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Wl.apply(null,arguments)}var om=["shift","alt","meta","mod","ctrl"],rS={esc:"escape",return:"enter",".":"period",",":"comma","-":"slash"," ":"space","`":"backquote","#":"backslash","+":"bracketright",ShiftLeft:"shift",ShiftRight:"shift",AltLeft:"alt",AltRight:"alt",MetaLeft:"meta",MetaRight:"meta",OSLeft:"meta",OSRight:"meta",ControlLeft:"ctrl",ControlRight:"ctrl"};function At(e){return(e&&rS[e]||e||"").trim().toLowerCase().replace(/key|digit|numpad|arrow/,"")}function iS(e){return om.includes(e)}function _s(e,t){return t===void 0&&(t=","),e.split(t)}function Ns(e,t,n){t===void 0&&(t="+");var r=e.toLocaleLowerCase().split(t).map(function(s){return At(s)}),i={alt:r.includes("alt"),ctrl:r.includes("ctrl")||r.includes("control"),shift:r.includes("shift"),meta:r.includes("meta"),mod:r.includes("mod")},o=r.filter(function(s){return!om.includes(s)});return Wl({},i,{keys:o,description:n,hotkey:e})}(function(){typeof document<"u"&&(document.addEventListener("keydown",function(e){e.key!==void 0&&sm([At(e.key),At(e.code)])}),document.addEventListener("keyup",function(e){e.key!==void 0&&lm([At(e.key),At(e.code)])})),typeof window<"u"&&window.addEventListener("blur",function(){Rt.clear()})})();var Rt=new Set;function lu(e){return Array.isArray(e)}function oS(e,t){t===void 0&&(t=",");var n=lu(e)?e:e.split(t);return n.every(function(r){return Rt.has(r.trim().toLowerCase())})}function sm(e){var t=Array.isArray(e)?e:[e];Rt.has("meta")&&Rt.forEach(function(n){return!iS(n)&&Rt.delete(n.toLowerCase())}),t.forEach(function(n){return Rt.add(n.toLowerCase())})}function lm(e){var t=Array.isArray(e)?e:[e];e==="meta"?Rt.clear():t.forEach(function(n){return Rt.delete(n.toLowerCase())})}function sS(e,t,n){(typeof n=="function"&&n(e,t)||n===!0)&&e.preventDefault()}function lS(e,t,n){return typeof n=="function"?n(e,t):n===!0||n===void 0}function aS(e){return am(e,["input","textarea","select"])}function am(e,t){t===void 0&&(t=!1);var n=e.target,r=e.composed,i=null;return uS(n)&&r?i=e.composedPath()[0]&&e.composedPath()[0].tagName:i=n&&n.tagName,lu(t)?!!(i&&t&&t.some(function(o){var s;return o.toLowerCase()===((s=i)==null?void 0:s.toLowerCase())})):!!(i&&t&&t)}function uS(e){return!!e.tagName&&!e.tagName.startsWith("-")&&e.tagName.includes("-")}function cS(e,t){return e.length===0&&t?(console.warn('A hotkey has the "scopes" option set, however no active scopes were found. If you want to use the global scopes feature, you need to wrap your app in a <HotkeysProvider>'),!0):t?e.some(function(n){return t.includes(n)})||e.includes("*"):!0}var fS=function(t,n,r){r===void 0&&(r=!1);var i=n.alt,o=n.meta,s=n.mod,l=n.shift,a=n.ctrl,u=n.keys,f=t.key,c=t.code,p=t.ctrlKey,m=t.metaKey,y=t.shiftKey,x=t.altKey,C=At(c),g=f.toLowerCase();if(!(u!=null&&u.includes(C))&&!(u!=null&&u.includes(g))&&!["ctrl","control","unknown","meta","alt","shift","os"].includes(C))return!1;if(!r){if(i===!x&&g!=="alt"||l===!y&&g!=="shift")return!1;if(s){if(!m&&!p)return!1}else if(o===!m&&g!=="meta"&&g!=="os"||a===!p&&g!=="ctrl"&&g!=="control")return!1}return u&&u.length===1&&(u.includes(g)||u.includes(C))?!0:u?oS(u):!u},dS=k.createContext(void 0),pS=function(){return k.useContext(dS)};function um(e,t){return e&&t&&typeof e=="object"&&typeof t=="object"?Object.keys(e).length===Object.keys(t).length&&Object.keys(e).reduce(function(n,r){return n&&um(e[r],t[r])},!0):e===t}var hS=k.createContext({hotkeys:[],enabledScopes:[],toggleScope:function(){},enableScope:function(){},disableScope:function(){}}),mS=function(){return k.useContext(hS)};function gS(e){var t=k.useRef(void 0);return um(t.current,e)||(t.current=e),t.current}var Cf=function(t){t.stopPropagation(),t.preventDefault(),t.stopImmediatePropagation()},yS=typeof window<"u"?k.useLayoutEffect:k.useEffect;function js(e,t,n,r){var i=k.useState(null),o=i[0],s=i[1],l=k.useRef(!1),a=n instanceof Array?r instanceof Array?void 0:r:n,u=lu(e)?e.join(a==null?void 0:a.splitKey):e,f=n instanceof Array?n:r instanceof Array?r:void 0,c=k.useCallback(t,f!=null?f:[]),p=k.useRef(c);f?p.current=c:p.current=t;var m=gS(a),y=mS(),x=y.enabledScopes,C=pS();return yS(function(){if(!((m==null?void 0:m.enabled)===!1||!cS(x,m==null?void 0:m.scopes))){var g=function(w,E){var P;if(E===void 0&&(E=!1),!(aS(w)&&!am(w,m==null?void 0:m.enableOnFormTags))){if(o!==null){var R=o.getRootNode();if((R instanceof Document||R instanceof ShadowRoot)&&R.activeElement!==o&&!o.contains(R.activeElement)){Cf(w);return}}(P=w.target)!=null&&P.isContentEditable&&!(m!=null&&m.enableOnContentEditable)||_s(u,m==null?void 0:m.splitKey).forEach(function(V){var J,K=Ns(V,m==null?void 0:m.combinationKey);if(fS(w,K,m==null?void 0:m.ignoreModifiers)||(J=K.keys)!=null&&J.includes("*")){if(m!=null&&m.ignoreEventWhen!=null&&m.ignoreEventWhen(w)||E&&l.current)return;if(sS(w,K,m==null?void 0:m.preventDefault),!lS(w,K,m==null?void 0:m.enabled)){Cf(w);return}p.current(w,K),E||(l.current=!0)}})}},d=function(w){w.key!==void 0&&(sm(At(w.code)),((m==null?void 0:m.keydown)===void 0&&(m==null?void 0:m.keyup)!==!0||m!=null&&m.keydown)&&g(w))},h=function(w){w.key!==void 0&&(lm(At(w.code)),l.current=!1,m!=null&&m.keyup&&g(w,!0))},v=o||(a==null?void 0:a.document)||document;return v.addEventListener("keyup",h,a==null?void 0:a.eventListenerOptions),v.addEventListener("keydown",d,a==null?void 0:a.eventListenerOptions),C&&_s(u,m==null?void 0:m.splitKey).forEach(function(S){return C.addHotkey(Ns(S,m==null?void 0:m.combinationKey,m==null?void 0:m.description))}),function(){v.removeEventListener("keyup",h,a==null?void 0:a.eventListenerOptions),v.removeEventListener("keydown",d,a==null?void 0:a.eventListenerOptions),C&&_s(u,m==null?void 0:m.splitKey).forEach(function(S){return C.removeHotkey(Ns(S,m==null?void 0:m.combinationKey,m==null?void 0:m.description))})}}},[o,u,m,x]),s}const vS=({isListening:e,onToggleListening:t,aiResponse:n,onCopySuggestion:r,onClose:i,onOpenSettings:o})=>{const[s,l]=k.useState(!1),a=f=>{r(f),l(!0),setTimeout(()=>l(!1),2e3)},u=f=>{};return D.jsxs("div",{className:"overlay-ui",children:[D.jsxs("div",{className:"overlay-header",children:[D.jsx("h3",{className:"overlay-title",children:"Cluely AI"}),D.jsxs("div",{style:{display:"flex",gap:"8px"},children:[D.jsx("button",{className:"close-button",onClick:o,title:"Settings",children:"⚙️"}),D.jsx("button",{className:"close-button",onClick:i,title:"Hide (Esc)",children:"×"})]})]}),D.jsxs("div",{className:"status-indicator",children:[D.jsx("div",{className:`status-dot ${e?"listening":""}`}),D.jsx("span",{className:"status-text",children:e?"Listening & Analyzing...":"Standby Mode"})]}),D.jsx("div",{className:"ai-response",children:n?D.jsxs(D.Fragment,{children:[D.jsx("div",{className:"ai-response-text",children:n}),D.jsx("button",{className:"copy-button",onClick:()=>a(n),title:"Copy to clipboard",children:s?"✓":"📋"})]}):D.jsx("div",{className:"ai-response-text ai-response-placeholder",children:e?"Analyzing your screen and audio for contextual suggestions...":"Press Cmd+Shift+L to start listening, or click the button below."})}),D.jsxs("div",{className:"controls",children:[D.jsx("button",{className:`control-button ${e?"danger":"primary"}`,onClick:t,children:e?"⏹ Stop":"🎤 Listen"}),D.jsx("button",{className:"control-button",onClick:()=>u(),disabled:!e,children:"📝 Summarize"}),D.jsx("button",{className:"control-button",onClick:()=>u(),disabled:!e,children:"❓ Answer"}),D.jsx("button",{className:"control-button",onClick:()=>u(),disabled:!e,children:"💡 Follow-up"})]}),D.jsxs("div",{style:{fontSize:"11px",color:"#666",textAlign:"center",marginTop:"12px",padding:"8px",background:"rgba(255, 255, 255, 0.02)",borderRadius:"6px"},children:["Session: ",new Date().toLocaleTimeString()," | Shortcuts: Cmd+Shift+A (toggle), Cmd+Shift+L (listen)"]})]})},xS=({sessionData:e,onSessionUpdate:t})=>{const[n,r]=k.useState(null),[i,o]=k.useState([]);k.useEffect(()=>{s(),a()},[]);const s=()=>{const h={id:l(),startTime:new Date().toISOString(),endTime:null,captures:[],aiResponses:[],userActions:[]};r(h)},l=()=>`session_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,a=async()=>{try{const d=localStorage.getItem("cluely-session-history");d&&o(JSON.parse(d))}catch(d){console.error("Failed to load session history:",d)}},u=async d=>{if(n)try{const h={...n,...d,lastUpdated:new Date().toISOString()};r(h),await rn("save_session_data",{sessionId:h.id,data:JSON.stringify(h)});const v=i.map(S=>S.id===h.id?h:S);i.find(S=>S.id===h.id)||v.push(h),o(v),localStorage.setItem("cluely-session-history",JSON.stringify(v)),t(v)}catch(h){console.error("Failed to save session data:",h)}},f=d=>{if(!n)return;const h={id:`capture_${Date.now()}`,timestamp:new Date().toISOString(),type:d.type,data:d.data,metadata:d.metadata||{}},v=[...n.captures,h];u({captures:v})},c=d=>{if(!n)return;const h={id:`response_${Date.now()}`,timestamp:new Date().toISOString(),prompt:d.prompt,response:d.response,context:d.context,processingTime:d.processingTime},v=[...n.aiResponses,h];u({aiResponses:v})},p=d=>{if(!n)return;const h={id:`action_${Date.now()}`,timestamp:new Date().toISOString(),type:d.type,data:d.data},v=[...n.userActions,h];u({userActions:v})},m=()=>{if(!n)return;const d={...n,endTime:new Date().toISOString(),duration:Date.now()-new Date(n.startTime).getTime()};u(d),r(null)},y=async()=>{if(!n)return null;try{return{sessionId:n.id,duration:n.endTime?new Date(n.endTime)-new Date(n.startTime):Date.now()-new Date(n.startTime).getTime(),totalCaptures:n.captures.length,totalAIResponses:n.aiResponses.length,totalUserActions:n.userActions.length,keyInsights:await x(),followUpSuggestions:await C()}}catch(d){return console.error("Failed to generate session summary:",d),null}},x=async()=>["User was primarily focused on coding tasks","Multiple questions about React components","Session lasted 45 minutes with high engagement"],C=async()=>["Review React best practices documentation","Consider implementing unit tests for new components","Schedule follow-up meeting to discuss architecture decisions"],g=d=>{const h=i.find(P=>P.id===d);if(!h)return;const v={...h,exportedAt:new Date().toISOString(),version:"1.0"},S=new Blob([JSON.stringify(v,null,2)],{type:"application/json"}),w=URL.createObjectURL(S),E=document.createElement("a");E.href=w,E.download=`cluely-session-${d}.json`,E.click(),URL.revokeObjectURL(w)};return Vr.useImperativeHandle(Vr.forwardRef(()=>null),()=>({addCapture:f,addAIResponse:c,addUserAction:p,endSession:m,generateSessionSummary:y,exportSessionData:g,currentSession:n,sessionHistory:i})),null},SS=({onClose:e})=>{const[t,n]=k.useState({geminiApiKey:"",mongoDbUri:"",captureInterval:2e3,enableScreenCapture:!0,enableAudioCapture:!0,enableOCR:!0,overlayOpacity:.85,autoStartListening:!1,saveSessionHistory:!0,enableNotifications:!0,hotkeysEnabled:!0,stealthMode:!0}),[r,i]=k.useState("general");k.useEffect(()=>{o()},[]);const o=async()=>{try{const c=localStorage.getItem("cluely-settings");c&&n(JSON.parse(c))}catch(c){console.error("Failed to load settings:",c)}},s=async()=>{try{localStorage.setItem("cluely-settings",JSON.stringify(t)),console.log("Settings saved successfully")}catch(c){console.error("Failed to save settings:",c)}},l=(c,p)=>{n(m=>({...m,[c]:p}))},a=()=>D.jsxs("div",{className:"settings-section",children:[D.jsx("h4",{children:"General Settings"}),D.jsxs("div",{className:"setting-item",children:[D.jsx("label",{children:"Capture Interval (ms)"}),D.jsx("input",{type:"number",value:t.captureInterval,onChange:c=>l("captureInterval",parseInt(c.target.value)),min:"1000",max:"10000",step:"500"})]}),D.jsxs("div",{className:"setting-item",children:[D.jsx("label",{children:"Overlay Opacity"}),D.jsx("input",{type:"range",min:"0.3",max:"1",step:"0.05",value:t.overlayOpacity,onChange:c=>l("overlayOpacity",parseFloat(c.target.value))}),D.jsxs("span",{children:[Math.round(t.overlayOpacity*100),"%"]})]}),D.jsxs("div",{className:"setting-item checkbox",children:[D.jsx("input",{type:"checkbox",checked:t.autoStartListening,onChange:c=>l("autoStartListening",c.target.checked)}),D.jsx("label",{children:"Auto-start listening on launch"})]}),D.jsxs("div",{className:"setting-item checkbox",children:[D.jsx("input",{type:"checkbox",checked:t.stealthMode,onChange:c=>l("stealthMode",c.target.checked)}),D.jsx("label",{children:"Stealth mode (hide from screen sharing)"})]}),D.jsxs("div",{className:"setting-item checkbox",children:[D.jsx("input",{type:"checkbox",checked:t.hotkeysEnabled,onChange:c=>l("hotkeysEnabled",c.target.checked)}),D.jsx("label",{children:"Enable global hotkeys"})]})]}),u=()=>D.jsxs("div",{className:"settings-section",children:[D.jsx("h4",{children:"Capture Settings"}),D.jsxs("div",{className:"setting-item checkbox",children:[D.jsx("input",{type:"checkbox",checked:t.enableScreenCapture,onChange:c=>l("enableScreenCapture",c.target.checked)}),D.jsx("label",{children:"Enable screen capture"})]}),D.jsxs("div",{className:"setting-item checkbox",children:[D.jsx("input",{type:"checkbox",checked:t.enableAudioCapture,onChange:c=>l("enableAudioCapture",c.target.checked)}),D.jsx("label",{children:"Enable audio capture"})]}),D.jsxs("div",{className:"setting-item checkbox",children:[D.jsx("input",{type:"checkbox",checked:t.enableOCR,onChange:c=>l("enableOCR",c.target.checked)}),D.jsx("label",{children:"Enable OCR text extraction"})]}),D.jsxs("div",{className:"setting-item checkbox",children:[D.jsx("input",{type:"checkbox",checked:t.saveSessionHistory,onChange:c=>l("saveSessionHistory",c.target.checked)}),D.jsx("label",{children:"Save session history"})]})]}),f=()=>D.jsxs("div",{className:"settings-section",children:[D.jsx("h4",{children:"API Configuration"}),D.jsxs("div",{className:"setting-item",children:[D.jsx("label",{children:"Gemini AI API Key"}),D.jsx("input",{type:"password",value:t.geminiApiKey,onChange:c=>l("geminiApiKey",c.target.value),placeholder:"Enter your Gemini API key"})]}),D.jsxs("div",{className:"setting-item",children:[D.jsx("label",{children:"MongoDB Connection URI"}),D.jsx("input",{type:"password",value:t.mongoDbUri,onChange:c=>l("mongoDbUri",c.target.value),placeholder:"mongodb://localhost:27017/cluely"})]}),D.jsxs("div",{className:"api-status",children:[D.jsxs("div",{className:"status-indicator",children:[D.jsx("div",{className:"status-dot"}),D.jsx("span",{children:"API Status: Not connected"})]}),D.jsx("button",{className:"test-button",children:"Test Connection"})]})]});return D.jsxs(Hl.div,{className:"settings-panel",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:[D.jsxs("div",{className:"settings-header",children:[D.jsx("h3",{children:"Settings"}),D.jsx("button",{className:"close-button",onClick:e,children:"×"})]}),D.jsxs("div",{className:"settings-tabs",children:[D.jsx("button",{className:`tab ${r==="general"?"active":""}`,onClick:()=>i("general"),children:"General"}),D.jsx("button",{className:`tab ${r==="capture"?"active":""}`,onClick:()=>i("capture"),children:"Capture"}),D.jsx("button",{className:`tab ${r==="api"?"active":""}`,onClick:()=>i("api"),children:"API"})]}),D.jsxs("div",{className:"settings-content",children:[r==="general"&&a(),r==="capture"&&u(),r==="api"&&f()]}),D.jsxs("div",{className:"settings-footer",children:[D.jsx("button",{className:"save-button",onClick:s,children:"Save Settings"}),D.jsx("button",{className:"cancel-button",onClick:e,children:"Cancel"})]}),D.jsx("style",{jsx:!0,children:`
        .settings-panel {
          padding: 20px;
          color: white;
          height: 100%;
          display: flex;
          flex-direction: column;
        }

        .settings-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
        }

        .settings-tabs {
          display: flex;
          gap: 4px;
          margin-bottom: 20px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .tab {
          background: none;
          border: none;
          color: #888;
          padding: 8px 16px;
          cursor: pointer;
          border-bottom: 2px solid transparent;
          transition: all 0.2s;
        }

        .tab.active {
          color: white;
          border-bottom-color: #007AFF;
        }

        .settings-content {
          flex: 1;
          overflow-y: auto;
        }

        .settings-section h4 {
          margin-bottom: 16px;
          color: #ccc;
          font-size: 14px;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .setting-item {
          margin-bottom: 16px;
        }

        .setting-item label {
          display: block;
          margin-bottom: 4px;
          font-size: 13px;
          color: #ccc;
        }

        .setting-item input[type="text"],
        .setting-item input[type="password"],
        .setting-item input[type="number"] {
          width: 100%;
          padding: 8px 12px;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 6px;
          color: white;
          font-size: 13px;
        }

        .setting-item input[type="range"] {
          width: calc(100% - 40px);
          margin-right: 8px;
        }

        .setting-item.checkbox {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .setting-item.checkbox input {
          width: auto;
        }

        .setting-item.checkbox label {
          margin: 0;
        }

        .api-status {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 16px;
          padding: 12px;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 6px;
        }

        .test-button {
          background: rgba(0, 122, 255, 0.8);
          border: none;
          color: white;
          padding: 6px 12px;
          border-radius: 4px;
          font-size: 12px;
          cursor: pointer;
        }

        .settings-footer {
          display: flex;
          gap: 8px;
          margin-top: 20px;
          padding-top: 16px;
          border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .save-button {
          background: rgba(0, 122, 255, 0.8);
          border: none;
          color: white;
          padding: 10px 20px;
          border-radius: 6px;
          cursor: pointer;
          flex: 1;
        }

        .cancel-button {
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: white;
          padding: 10px 20px;
          border-radius: 6px;
          cursor: pointer;
          flex: 1;
        }
      `})]})};function wS(){const[e,t]=k.useState(!1),[n,r]=k.useState(!1),[i,o]=k.useState(""),[s,l]=k.useState([]),[a,u]=k.useState(!1),[f,c]=k.useState("");k.useEffect(()=>{const g=xs("toggle-overlay",()=>{t(v=>!v)}),d=xs("quick-listen",()=>{p()}),h=xs("toggle-listening",()=>{p()});return()=>{g.then(v=>v()),d.then(v=>v()),h.then(v=>v())}},[]),js("escape",()=>t(!1),{enabled:e}),js("ctrl+l",p,{enabled:e}),js("ctrl+s",()=>u(g=>!g),{enabled:e});async function p(){try{const g=await rn("toggle_listening");r(g),g&&(await Promise.all([rn("start_screen_capture"),rn("start_audio_capture")]),m())}catch(g){console.error("Failed to toggle listening:",g)}}async function m(){const g=setInterval(async()=>{if(!n){clearInterval(g);return}try{const d=await y(),h=await rn("send_to_ai",{context:d});c(h);const v=x();await rn("save_session_data",{sessionId:v,data:JSON.stringify({context:d,response:h,timestamp:Date.now()})})}catch(d){console.error("Error processing data:",d)}},2e3)}async function y(){return"Current screen context and audio data would be gathered here"}function x(){return`session_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}function C(g){navigator.clipboard.writeText(g),o("Copied to clipboard!"),setTimeout(()=>o(""),2e3)}return D.jsxs("div",{className:"app",children:[D.jsx(kf,{children:e&&D.jsx(Hl.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},transition:{duration:.2},className:"overlay-container",children:D.jsx(vS,{isListening:n,onToggleListening:p,aiResponse:f,onCopySuggestion:C,onClose:()=>t(!1),onOpenSettings:()=>u(!0)})})}),D.jsx(kf,{children:a&&D.jsx(Hl.div,{initial:{opacity:0,x:300},animate:{opacity:1,x:0},exit:{opacity:0,x:300},transition:{duration:.3},className:"settings-container",children:D.jsx(SS,{onClose:()=>u(!1)})})}),D.jsx(xS,{sessionData:s,onSessionUpdate:l})]})}Os.createRoot(document.getElementById("root")).render(D.jsx(Vr.StrictMode,{children:D.jsx(wS,{})}));
