#!/usr/bin/env python3
from PIL import Image, ImageDraw, ImageFont
import os

# Create icons directory
os.makedirs('src-tauri/icons', exist_ok=True)

# Create simple black icons with white "C" text
sizes = [32, 128, 256]
for size in sizes:
    # Create a black image with alpha channel (RGBA)
    img = Image.new('RGBA', (size, size), color=(0, 0, 0, 255))
    draw = ImageDraw.Draw(img)
    
    # Try to use a default font, fallback to basic if not available
    try:
        font_size = size // 2
        font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", font_size)
    except:
        font = ImageFont.load_default()
    
    # Draw white "C" in center
    text = "C"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (size - text_width) // 2
    y = (size - text_height) // 2
    
    draw.text((x, y), text, fill='white', font=font)
    
    # Save the image
    if size == 32:
        img.save('src-tauri/icons/32x32.png')
        img.save('src-tauri/icons/icon.png')
    elif size == 128:
        img.save('src-tauri/icons/128x128.png')
    elif size == 256:
        img.save('src-tauri/icons/<EMAIL>')

print("Icons created successfully!")
