/* Global styles for the Cluely AI Assistant */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: transparent;
  overflow: hidden;
}

.app {
  width: 100vw;
  height: 100vh;
  background: transparent;
  position: relative;
}

/* Overlay Container */
.overlay-container {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 350px;
  max-height: 80vh;
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  z-index: 10000;
  overflow: hidden;
}

/* Settings Container */
.settings-container {
  position: fixed;
  top: 20px;
  right: 380px;
  width: 300px;
  max-height: 80vh;
  background: rgba(20, 20, 20, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  z-index: 9999;
  overflow: hidden;
}

/* Overlay UI Styles */
.overlay-ui {
  padding: 20px;
  color: white;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.overlay-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.overlay-title {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
}

.close-button {
  background: none;
  border: none;
  color: #888;
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s;
}

.close-button:hover {
  color: #ffffff;
}

/* Status Indicator */
.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ff4444;
  transition: background-color 0.3s;
}

.status-dot.listening {
  background: #44ff44;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.status-text {
  font-size: 14px;
  color: #cccccc;
}

/* AI Response Area */
.ai-response {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 120px;
  max-height: 300px;
  overflow-y: auto;
}

.ai-response-text {
  font-size: 14px;
  line-height: 1.5;
  color: #ffffff;
  white-space: pre-wrap;
}

.ai-response-placeholder {
  color: #888;
  font-style: italic;
}

/* Control Buttons */
.controls {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.control-button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  flex: 1;
  min-width: 80px;
}

.control-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.control-button.primary {
  background: rgba(0, 122, 255, 0.8);
  border-color: rgba(0, 122, 255, 1);
}

.control-button.primary:hover {
  background: rgba(0, 122, 255, 1);
}

.control-button.danger {
  background: rgba(255, 59, 48, 0.8);
  border-color: rgba(255, 59, 48, 1);
}

.control-button.danger:hover {
  background: rgba(255, 59, 48, 1);
}

/* Copy Button */
.copy-button {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s;
}

.ai-response:hover .copy-button {
  opacity: 1;
}

.copy-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Scrollbar Styling */
.ai-response::-webkit-scrollbar {
  width: 4px;
}

.ai-response::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.ai-response::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.ai-response::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
  .overlay-container {
    width: calc(100vw - 40px);
    right: 20px;
    left: 20px;
  }
  
  .settings-container {
    width: calc(100vw - 40px);
    right: 20px;
    left: 20px;
    top: 100px;
  }
}
