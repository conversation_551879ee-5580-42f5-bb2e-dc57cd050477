import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import { listen } from '@tauri-apps/api/event';
import { motion, AnimatePresence } from 'framer-motion';
import { useHotkeys } from 'react-hotkeys-hook';
import OverlayUI from './components/OverlayUI';
import SessionManager from './components/SessionManager';
import SettingsPanel from './components/SettingsPanel';
import './App.css';

function App() {
  const [isVisible, setIsVisible] = useState(true); // Start visible for testing
  const [isListening, setIsListening] = useState(false);
  const [currentSuggestion, setCurrentSuggestion] = useState('');
  const [sessionData, setSessionData] = useState([]);
  const [showSettings, setShowSettings] = useState(false);
  const [aiResponse, setAiResponse] = useState('Welcome to Cluely AI Assistant! Click the Listen button to start capturing your screen and audio for AI-powered suggestions.');

  // Listen for global shortcut events from Rust backend
  useEffect(() => {
    const unlistenToggle = listen('toggle-overlay', () => {
      setIsVisible(prev => !prev);
    });

    const unlistenListen = listen('quick-listen', () => {
      handleToggleListening();
    });

    const unlistenToggleListening = listen('toggle-listening', () => {
      handleToggleListening();
    });

    return () => {
      unlistenToggle.then(fn => fn());
      unlistenListen.then(fn => fn());
      unlistenToggleListening.then(fn => fn());
    };
  }, []);

  // Hotkeys for when the overlay is visible
  useHotkeys('escape', () => setIsVisible(false), { enabled: isVisible });
  useHotkeys('ctrl+l', handleToggleListening, { enabled: isVisible });
  useHotkeys('ctrl+s', () => setShowSettings(prev => !prev), { enabled: isVisible });

  async function handleToggleListening() {
    try {
      const newState = await invoke('toggle_listening');
      setIsListening(newState);
      
      if (newState) {
        // Start capturing when listening begins
        await Promise.all([
          invoke('start_screen_capture'),
          invoke('start_audio_capture')
        ]);
        
        // Start processing captured data
        startDataProcessing();
      }
    } catch (error) {
      console.error('Failed to toggle listening:', error);
    }
  }

  async function startDataProcessing() {
    // This would be called periodically to process captured data
    const interval = setInterval(async () => {
      if (!isListening) {
        clearInterval(interval);
        return;
      }

      try {
        // Get current context (this would be implemented to gather screen + audio data)
        const context = await gatherCurrentContext();
        
        // Send to AI for processing
        const response = await invoke('send_to_ai', { context });
        setAiResponse(response);
        
        // Save session data
        const sessionId = generateSessionId();
        await invoke('save_session_data', { 
          sessionId, 
          data: JSON.stringify({ context, response, timestamp: Date.now() })
        });
        
      } catch (error) {
        console.error('Error processing data:', error);
      }
    }, 2000); // Process every 2 seconds
  }

  async function gatherCurrentContext() {
    // This would gather screen capture data, audio transcripts, etc.
    // For now, return a placeholder
    return "Current screen context and audio data would be gathered here";
  }

  function generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  function handleCopySuggestion(text) {
    navigator.clipboard.writeText(text);
    // Show brief feedback
    setCurrentSuggestion('Copied to clipboard!');
    setTimeout(() => setCurrentSuggestion(''), 2000);
  }

  return (
    <div className="app">
      {/* Debug info */}
      <div style={{
        position: 'fixed',
        top: '10px',
        left: '10px',
        background: 'rgba(0,0,0,0.8)',
        color: 'white',
        padding: '10px',
        borderRadius: '5px',
        fontSize: '12px',
        zIndex: 9999
      }}>
        Debug: isVisible={isVisible.toString()}, isListening={isListening.toString()}
      </div>

      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.2 }}
            className="overlay-container"
          >
            <OverlayUI
              isListening={isListening}
              onToggleListening={handleToggleListening}
              aiResponse={aiResponse}
              onCopySuggestion={handleCopySuggestion}
              onClose={() => setIsVisible(false)}
              onOpenSettings={() => setShowSettings(true)}
            />
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {showSettings && (
          <motion.div
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            transition={{ duration: 0.3 }}
            className="settings-container"
          >
            <SettingsPanel
              onClose={() => setShowSettings(false)}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Hidden session manager for background data handling */}
      <SessionManager
        sessionData={sessionData}
        onSessionUpdate={setSessionData}
      />
    </div>
  );
}

export default App;
