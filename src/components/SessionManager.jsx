import React, { useEffect, useState } from 'react';
import { invoke } from '@tauri-apps/api/tauri';

const SessionManager = ({ sessionData, onSessionUpdate }) => {
  const [currentSession, setCurrentSession] = useState(null);
  const [sessionHistory, setSessionHistory] = useState([]);

  useEffect(() => {
    initializeSession();
    loadSessionHistory();
  }, []);

  const initializeSession = () => {
    const sessionId = generateSessionId();
    const newSession = {
      id: sessionId,
      startTime: new Date().toISOString(),
      endTime: null,
      captures: [],
      aiResponses: [],
      userActions: []
    };
    
    setCurrentSession(newSession);
  };

  const generateSessionId = () => {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  const loadSessionHistory = async () => {
    try {
      // Load from local storage for now, later from MongoDB
      const history = localStorage.getItem('cluely-session-history');
      if (history) {
        setSessionHistory(JSON.parse(history));
      }
    } catch (error) {
      console.error('Failed to load session history:', error);
    }
  };

  const saveSessionData = async (data) => {
    if (!currentSession) return;

    try {
      const updatedSession = {
        ...currentSession,
        ...data,
        lastUpdated: new Date().toISOString()
      };

      setCurrentSession(updatedSession);
      
      // Save to backend
      await invoke('save_session_data', {
        sessionId: updatedSession.id,
        data: JSON.stringify(updatedSession)
      });

      // Update local storage as backup
      const updatedHistory = sessionHistory.map(session => 
        session.id === updatedSession.id ? updatedSession : session
      );
      
      if (!sessionHistory.find(s => s.id === updatedSession.id)) {
        updatedHistory.push(updatedSession);
      }

      setSessionHistory(updatedHistory);
      localStorage.setItem('cluely-session-history', JSON.stringify(updatedHistory));
      
      onSessionUpdate(updatedHistory);
    } catch (error) {
      console.error('Failed to save session data:', error);
    }
  };

  const addCapture = (captureData) => {
    if (!currentSession) return;

    const capture = {
      id: `capture_${Date.now()}`,
      timestamp: new Date().toISOString(),
      type: captureData.type, // 'screen' or 'audio'
      data: captureData.data,
      metadata: captureData.metadata || {}
    };

    const updatedCaptures = [...currentSession.captures, capture];
    saveSessionData({ captures: updatedCaptures });
  };

  const addAIResponse = (responseData) => {
    if (!currentSession) return;

    const response = {
      id: `response_${Date.now()}`,
      timestamp: new Date().toISOString(),
      prompt: responseData.prompt,
      response: responseData.response,
      context: responseData.context,
      processingTime: responseData.processingTime
    };

    const updatedResponses = [...currentSession.aiResponses, response];
    saveSessionData({ aiResponses: updatedResponses });
  };

  const addUserAction = (actionData) => {
    if (!currentSession) return;

    const action = {
      id: `action_${Date.now()}`,
      timestamp: new Date().toISOString(),
      type: actionData.type, // 'copy', 'listen_start', 'listen_stop', etc.
      data: actionData.data
    };

    const updatedActions = [...currentSession.userActions, action];
    saveSessionData({ userActions: updatedActions });
  };

  const endSession = () => {
    if (!currentSession) return;

    const endedSession = {
      ...currentSession,
      endTime: new Date().toISOString(),
      duration: Date.now() - new Date(currentSession.startTime).getTime()
    };

    saveSessionData(endedSession);
    setCurrentSession(null);
  };

  const generateSessionSummary = async () => {
    if (!currentSession) return null;

    try {
      const summary = {
        sessionId: currentSession.id,
        duration: currentSession.endTime 
          ? new Date(currentSession.endTime) - new Date(currentSession.startTime)
          : Date.now() - new Date(currentSession.startTime).getTime(),
        totalCaptures: currentSession.captures.length,
        totalAIResponses: currentSession.aiResponses.length,
        totalUserActions: currentSession.userActions.length,
        keyInsights: await extractKeyInsights(),
        followUpSuggestions: await generateFollowUps()
      };

      return summary;
    } catch (error) {
      console.error('Failed to generate session summary:', error);
      return null;
    }
  };

  const extractKeyInsights = async () => {
    // This would use AI to analyze the session data and extract key insights
    return [
      'User was primarily focused on coding tasks',
      'Multiple questions about React components',
      'Session lasted 45 minutes with high engagement'
    ];
  };

  const generateFollowUps = async () => {
    // This would use AI to generate follow-up suggestions
    return [
      'Review React best practices documentation',
      'Consider implementing unit tests for new components',
      'Schedule follow-up meeting to discuss architecture decisions'
    ];
  };

  const exportSessionData = (sessionId) => {
    const session = sessionHistory.find(s => s.id === sessionId);
    if (!session) return;

    const exportData = {
      ...session,
      exportedAt: new Date().toISOString(),
      version: '1.0'
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `cluely-session-${sessionId}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // Expose methods for other components to use
  React.useImperativeHandle(React.forwardRef(() => null), () => ({
    addCapture,
    addAIResponse,
    addUserAction,
    endSession,
    generateSessionSummary,
    exportSessionData,
    currentSession,
    sessionHistory
  }));

  // This component doesn't render anything visible
  return null;
};

export default SessionManager;
