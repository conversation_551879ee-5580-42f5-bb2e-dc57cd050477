import React, { useState } from 'react';
import { motion } from 'framer-motion';

const OverlayUI = ({ 
  isListening, 
  onToggleListening, 
  aiResponse, 
  onCopySuggestion, 
  onClose, 
  onOpenSettings 
}) => {
  const [showCopyFeedback, setShowCopyFeedback] = useState(false);

  const handleCopy = (text) => {
    onCopySuggestion(text);
    setShowCopyFeedback(true);
    setTimeout(() => setShowCopyFeedback(false), 2000);
  };

  const handleQuickActions = (action) => {
    switch (action) {
      case 'summarize':
        // Trigger summarization of current context
        break;
      case 'answer':
        // Trigger quick answer mode
        break;
      case 'followup':
        // Generate follow-up suggestions
        break;
      default:
        break;
    }
  };

  return (
    <div className="overlay-ui">
      {/* Header */}
      <div className="overlay-header">
        <h3 className="overlay-title">Cluely AI</h3>
        <div style={{ display: 'flex', gap: '8px' }}>
          <button 
            className="close-button"
            onClick={onOpenSettings}
            title="Settings"
          >
            ⚙️
          </button>
          <button 
            className="close-button"
            onClick={onClose}
            title="Hide (Esc)"
          >
            ×
          </button>
        </div>
      </div>

      {/* Status Indicator */}
      <div className="status-indicator">
        <div className={`status-dot ${isListening ? 'listening' : ''}`}></div>
        <span className="status-text">
          {isListening ? 'Listening & Analyzing...' : 'Standby Mode'}
        </span>
      </div>

      {/* AI Response Area */}
      <div className="ai-response">
        {aiResponse ? (
          <>
            <div className="ai-response-text">{aiResponse}</div>
            <button 
              className="copy-button"
              onClick={() => handleCopy(aiResponse)}
              title="Copy to clipboard"
            >
              {showCopyFeedback ? '✓' : '📋'}
            </button>
          </>
        ) : (
          <div className="ai-response-text ai-response-placeholder">
            {isListening 
              ? 'Analyzing your screen and audio for contextual suggestions...'
              : 'Press Cmd+Shift+L to start listening, or click the button below.'
            }
          </div>
        )}
      </div>

      {/* Control Buttons */}
      <div className="controls">
        <button 
          className={`control-button ${isListening ? 'danger' : 'primary'}`}
          onClick={onToggleListening}
        >
          {isListening ? '⏹ Stop' : '🎤 Listen'}
        </button>
        
        <button 
          className="control-button"
          onClick={() => handleQuickActions('summarize')}
          disabled={!isListening}
        >
          📝 Summarize
        </button>
        
        <button 
          className="control-button"
          onClick={() => handleQuickActions('answer')}
          disabled={!isListening}
        >
          ❓ Answer
        </button>
        
        <button 
          className="control-button"
          onClick={() => handleQuickActions('followup')}
          disabled={!isListening}
        >
          💡 Follow-up
        </button>
      </div>

      {/* Quick Stats */}
      <div style={{ 
        fontSize: '11px', 
        color: '#666', 
        textAlign: 'center',
        marginTop: '12px',
        padding: '8px',
        background: 'rgba(255, 255, 255, 0.02)',
        borderRadius: '6px'
      }}>
        Session: {new Date().toLocaleTimeString()} | 
        Shortcuts: Cmd+Shift+A (toggle), Cmd+Shift+L (listen)
      </div>
    </div>
  );
};

export default OverlayUI;
