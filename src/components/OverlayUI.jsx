import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { invoke } from '@tauri-apps/api/tauri';

const OverlayUI = ({
  isListening,
  onToggleListening,
  aiResponse,
  onCopySuggestion,
  onClose,
  onOpenSettings
}) => {
  const [showCopyFeedback, setShowCopyFeedback] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [alwaysListening, setAlwaysListening] = useState(true);
  const [lastActivity, setLastActivity] = useState(Date.now());

  const handleCopy = (text) => {
    onCopySuggestion(text);
    setShowCopyFeedback(true);
    setTimeout(() => setShowCopyFeedback(false), 2000);
  };

  const handleQuickActions = async (action) => {
    setIsProcessing(true);
    try {
      switch (action) {
        case 'summarize':
          const summary = await invoke('get_session_summary');
          // Update the parent component with the response
          if (onCopySuggestion) {
            onCopySuggestion(summary);
          }
          break;
        case 'answer':
          const answer = await invoke('process_ai_request', {
            prompt: 'Please analyze the current context and provide helpful suggestions.'
          });
          if (onCopySuggestion) {
            onCopySuggestion(answer);
          }
          break;
        case 'followup':
          const followup = await invoke('process_ai_request', {
            prompt: 'Based on our previous conversation, what are the next recommended steps?'
          });
          if (onCopySuggestion) {
            onCopySuggestion(followup);
          }
          break;
        default:
          break;
      }
    } catch (error) {
      console.error('Error in quick action:', error);
      if (onCopySuggestion) {
        onCopySuggestion(`Error: ${error}`);
      }
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="overlay-ui">
      {/* Header */}
      <div className="overlay-header">
        <h3 className="overlay-title">Cluely AI</h3>
        <div style={{ display: 'flex', gap: '8px' }}>
          <button 
            className="close-button"
            onClick={onOpenSettings}
            title="Settings"
          >
            ⚙️
          </button>
          <button 
            className="close-button"
            onClick={onClose}
            title="Hide (Esc)"
          >
            ×
          </button>
        </div>
      </div>

      {/* Status Indicator */}
      <div className="status-indicator">
        <div className={`status-dot ${alwaysListening ? 'always-on' : 'paused'}`}></div>
        <span className="status-text">
          {alwaysListening ? 'Always Listening' : 'Paused'}
          {isProcessing && <span className="processing-pulse"> 🧠</span>}
        </span>
        <div className="listening-mode">
          <small style={{ color: '#888', fontSize: '11px' }}>
            {alwaysListening
              ? '🎤 Monitoring screen & audio • Ready for voice commands'
              : '⏸️ Background monitoring paused'
            }
          </small>
        </div>
      </div>

      {/* AI Response Area */}
      <div className="ai-response">
        {aiResponse ? (
          <>
            <div className="ai-response-text">{aiResponse}</div>
            <button 
              className="copy-button"
              onClick={() => handleCopy(aiResponse)}
              title="Copy to clipboard"
            >
              {showCopyFeedback ? '✓' : '📋'}
            </button>
          </>
        ) : (
          <div className="ai-response-text ai-response-placeholder">
            {alwaysListening
              ? 'Monitoring your screen and audio. Say "Hey Cluely" or click a button for assistance.'
              : 'Background monitoring paused. Click Resume to enable AI assistance.'
            }
          </div>
        )}
      </div>

      {/* Control Buttons */}
      <div className="controls">
        <button
          className={`control-button ${alwaysListening ? 'warning' : 'primary'}`}
          onClick={() => setAlwaysListening(!alwaysListening)}
          title={alwaysListening ? 'Pause background monitoring' : 'Resume background monitoring'}
        >
          {alwaysListening ? '⏸️ Pause' : '▶️ Resume'}
        </button>

        <button
          className="control-button"
          onClick={() => handleQuickActions('summarize')}
          disabled={isProcessing}
        >
          📊 Summarize
        </button>

        <button
          className="control-button"
          onClick={() => handleQuickActions('answer')}
          disabled={isProcessing}
        >
          💡 Answer
        </button>
        
        <button 
          className="control-button"
          onClick={() => handleQuickActions('followup')}
          disabled={!isListening}
        >
          💡 Follow-up
        </button>
      </div>

      {/* Quick Stats */}
      <div style={{ 
        fontSize: '11px', 
        color: '#666', 
        textAlign: 'center',
        marginTop: '12px',
        padding: '8px',
        background: 'rgba(255, 255, 255, 0.02)',
        borderRadius: '6px'
      }}>
        Session: {new Date().toLocaleTimeString()} | 
        Shortcuts: Cmd+Shift+A (toggle), Cmd+Shift+L (listen)
      </div>
    </div>
  );
};

export default OverlayUI;
