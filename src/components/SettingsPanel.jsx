import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const SettingsPanel = ({ onClose }) => {
  const [settings, setSettings] = useState({
    geminiApiKey: '',
    mongoDbUri: '',
    captureInterval: 2000,
    enableScreenCapture: true,
    enableAudioCapture: true,
    enableOCR: true,
    overlayOpacity: 0.85,
    autoStartListening: false,
    saveSessionHistory: true,
    enableNotifications: true,
    hotkeysEnabled: true,
    stealthMode: true
  });

  const [activeTab, setActiveTab] = useState('general');

  useEffect(() => {
    // Load settings from local storage or backend
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const savedSettings = localStorage.getItem('cluely-settings');
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  };

  const saveSettings = async () => {
    try {
      localStorage.setItem('cluely-settings', JSON.stringify(settings));
      // Also save to backend if needed
      console.log('Settings saved successfully');
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  };

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const renderGeneralSettings = () => (
    <div className="settings-section">
      <h4>General Settings</h4>
      
      <div className="setting-item">
        <label>Capture Interval (ms)</label>
        <input
          type="number"
          value={settings.captureInterval}
          onChange={(e) => handleSettingChange('captureInterval', parseInt(e.target.value))}
          min="1000"
          max="10000"
          step="500"
        />
      </div>

      <div className="setting-item">
        <label>Overlay Opacity</label>
        <input
          type="range"
          min="0.3"
          max="1"
          step="0.05"
          value={settings.overlayOpacity}
          onChange={(e) => handleSettingChange('overlayOpacity', parseFloat(e.target.value))}
        />
        <span>{Math.round(settings.overlayOpacity * 100)}%</span>
      </div>

      <div className="setting-item checkbox">
        <input
          type="checkbox"
          checked={settings.autoStartListening}
          onChange={(e) => handleSettingChange('autoStartListening', e.target.checked)}
        />
        <label>Auto-start listening on launch</label>
      </div>

      <div className="setting-item checkbox">
        <input
          type="checkbox"
          checked={settings.stealthMode}
          onChange={(e) => handleSettingChange('stealthMode', e.target.checked)}
        />
        <label>Stealth mode (hide from screen sharing)</label>
      </div>

      <div className="setting-item checkbox">
        <input
          type="checkbox"
          checked={settings.hotkeysEnabled}
          onChange={(e) => handleSettingChange('hotkeysEnabled', e.target.checked)}
        />
        <label>Enable global hotkeys</label>
      </div>
    </div>
  );

  const renderCaptureSettings = () => (
    <div className="settings-section">
      <h4>Capture Settings</h4>
      
      <div className="setting-item checkbox">
        <input
          type="checkbox"
          checked={settings.enableScreenCapture}
          onChange={(e) => handleSettingChange('enableScreenCapture', e.target.checked)}
        />
        <label>Enable screen capture</label>
      </div>

      <div className="setting-item checkbox">
        <input
          type="checkbox"
          checked={settings.enableAudioCapture}
          onChange={(e) => handleSettingChange('enableAudioCapture', e.target.checked)}
        />
        <label>Enable audio capture</label>
      </div>

      <div className="setting-item checkbox">
        <input
          type="checkbox"
          checked={settings.enableOCR}
          onChange={(e) => handleSettingChange('enableOCR', e.target.checked)}
        />
        <label>Enable OCR text extraction</label>
      </div>

      <div className="setting-item checkbox">
        <input
          type="checkbox"
          checked={settings.saveSessionHistory}
          onChange={(e) => handleSettingChange('saveSessionHistory', e.target.checked)}
        />
        <label>Save session history</label>
      </div>
    </div>
  );

  const renderApiSettings = () => (
    <div className="settings-section">
      <h4>API Configuration</h4>
      
      <div className="setting-item">
        <label>Gemini AI API Key</label>
        <input
          type="password"
          value={settings.geminiApiKey}
          onChange={(e) => handleSettingChange('geminiApiKey', e.target.value)}
          placeholder="Enter your Gemini API key"
        />
      </div>

      <div className="setting-item">
        <label>MongoDB Connection URI</label>
        <input
          type="password"
          value={settings.mongoDbUri}
          onChange={(e) => handleSettingChange('mongoDbUri', e.target.value)}
          placeholder="mongodb://localhost:27017/cluely"
        />
      </div>

      <div className="api-status">
        <div className="status-indicator">
          <div className="status-dot"></div>
          <span>API Status: Not connected</span>
        </div>
        <button className="test-button">Test Connection</button>
      </div>
    </div>
  );

  return (
    <motion.div 
      className="settings-panel"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <div className="settings-header">
        <h3>Settings</h3>
        <button className="close-button" onClick={onClose}>×</button>
      </div>

      <div className="settings-tabs">
        <button 
          className={`tab ${activeTab === 'general' ? 'active' : ''}`}
          onClick={() => setActiveTab('general')}
        >
          General
        </button>
        <button 
          className={`tab ${activeTab === 'capture' ? 'active' : ''}`}
          onClick={() => setActiveTab('capture')}
        >
          Capture
        </button>
        <button 
          className={`tab ${activeTab === 'api' ? 'active' : ''}`}
          onClick={() => setActiveTab('api')}
        >
          API
        </button>
      </div>

      <div className="settings-content">
        {activeTab === 'general' && renderGeneralSettings()}
        {activeTab === 'capture' && renderCaptureSettings()}
        {activeTab === 'api' && renderApiSettings()}
      </div>

      <div className="settings-footer">
        <button className="save-button" onClick={saveSettings}>
          Save Settings
        </button>
        <button className="cancel-button" onClick={onClose}>
          Cancel
        </button>
      </div>

      <style jsx>{`
        .settings-panel {
          padding: 20px;
          color: white;
          height: 100%;
          display: flex;
          flex-direction: column;
        }

        .settings-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
        }

        .settings-tabs {
          display: flex;
          gap: 4px;
          margin-bottom: 20px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .tab {
          background: none;
          border: none;
          color: #888;
          padding: 8px 16px;
          cursor: pointer;
          border-bottom: 2px solid transparent;
          transition: all 0.2s;
        }

        .tab.active {
          color: white;
          border-bottom-color: #007AFF;
        }

        .settings-content {
          flex: 1;
          overflow-y: auto;
        }

        .settings-section h4 {
          margin-bottom: 16px;
          color: #ccc;
          font-size: 14px;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .setting-item {
          margin-bottom: 16px;
        }

        .setting-item label {
          display: block;
          margin-bottom: 4px;
          font-size: 13px;
          color: #ccc;
        }

        .setting-item input[type="text"],
        .setting-item input[type="password"],
        .setting-item input[type="number"] {
          width: 100%;
          padding: 8px 12px;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 6px;
          color: white;
          font-size: 13px;
        }

        .setting-item input[type="range"] {
          width: calc(100% - 40px);
          margin-right: 8px;
        }

        .setting-item.checkbox {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .setting-item.checkbox input {
          width: auto;
        }

        .setting-item.checkbox label {
          margin: 0;
        }

        .api-status {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 16px;
          padding: 12px;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 6px;
        }

        .test-button {
          background: rgba(0, 122, 255, 0.8);
          border: none;
          color: white;
          padding: 6px 12px;
          border-radius: 4px;
          font-size: 12px;
          cursor: pointer;
        }

        .settings-footer {
          display: flex;
          gap: 8px;
          margin-top: 20px;
          padding-top: 16px;
          border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .save-button {
          background: rgba(0, 122, 255, 0.8);
          border: none;
          color: white;
          padding: 10px 20px;
          border-radius: 6px;
          cursor: pointer;
          flex: 1;
        }

        .cancel-button {
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: white;
          padding: 10px 20px;
          border-radius: 6px;
          cursor: pointer;
          flex: 1;
        }
      `}</style>
    </motion.div>
  );
};

export default SettingsPanel;
