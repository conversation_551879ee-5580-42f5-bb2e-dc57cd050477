import React, { useState } from 'react';
import { invoke } from '@tauri-apps/api/tauri';

const SystemTest = ({ onClose }) => {
  const [testResults, setTestResults] = useState({});
  const [isRunning, setIsRunning] = useState(false);

  const runTest = async (testName, testFunction) => {
    setIsRunning(true);
    setTestResults(prev => ({
      ...prev,
      [testName]: { status: 'running', result: 'Testing...' }
    }));

    try {
      const result = await testFunction();
      setTestResults(prev => ({
        ...prev,
        [testName]: { status: 'success', result }
      }));
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        [testName]: { status: 'error', result: error.toString() }
      }));
    }
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults({});

    // Test 1: Environment Variables
    await runTest('env', async () => {
      const settings = await invoke('load_settings');
      return `Environment check completed:\n${settings}`;
    });

    // Test 2: Gemini API
    await runTest('gemini', async () => {
      return await invoke('test_gemini_api');
    });

    // Test 3: MongoDB
    await runTest('mongodb', async () => {
      return await invoke('test_mongodb_connection');
    });

    // Test 4: Basic AI Request
    await runTest('ai_request', async () => {
      return await invoke('process_ai_request', { 
        prompt: 'System test: Please respond with "System test successful" to confirm AI processing is working.' 
      });
    });

    setIsRunning(false);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'running': return '⏳';
      case 'success': return '✅';
      case 'error': return '❌';
      default: return '⚪';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'running': return '#ffa500';
      case 'success': return '#00ff00';
      case 'error': return '#ff0000';
      default: return '#888888';
    }
  };

  return (
    <div style={{
      position: 'fixed',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      background: 'rgba(0, 0, 0, 0.95)',
      color: 'white',
      padding: '20px',
      borderRadius: '10px',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      minWidth: '600px',
      maxWidth: '800px',
      maxHeight: '80vh',
      overflow: 'auto',
      zIndex: 10000,
      fontFamily: 'monospace'
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h2 style={{ margin: 0 }}>🧪 Cluely System Test</h2>
        <button 
          onClick={onClose}
          style={{
            background: 'transparent',
            border: '1px solid rgba(255, 255, 255, 0.3)',
            color: 'white',
            padding: '5px 10px',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          ✕ Close
        </button>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={runAllTests}
          disabled={isRunning}
          style={{
            background: isRunning ? '#666' : '#007acc',
            color: 'white',
            border: 'none',
            padding: '10px 20px',
            borderRadius: '5px',
            cursor: isRunning ? 'not-allowed' : 'pointer',
            fontSize: '16px'
          }}
        >
          {isRunning ? '🔄 Running Tests...' : '🚀 Run All Tests'}
        </button>
      </div>

      <div style={{ display: 'grid', gap: '15px' }}>
        {[
          { key: 'env', name: 'Environment Variables', description: 'Check .env file loading' },
          { key: 'gemini', name: 'Gemini AI API', description: 'Test API key and connectivity' },
          { key: 'mongodb', name: 'MongoDB Connection', description: 'Validate database URI' },
          { key: 'ai_request', name: 'AI Processing', description: 'End-to-end AI request test' }
        ].map(test => (
          <div key={test.key} style={{
            background: 'rgba(255, 255, 255, 0.05)',
            padding: '15px',
            borderRadius: '8px',
            border: `1px solid ${getStatusColor(testResults[test.key]?.status || 'default')}`
          }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>
              <span style={{ fontSize: '20px', marginRight: '10px' }}>
                {getStatusIcon(testResults[test.key]?.status)}
              </span>
              <div>
                <h3 style={{ margin: 0, fontSize: '16px' }}>{test.name}</h3>
                <p style={{ margin: 0, fontSize: '12px', color: '#aaa' }}>{test.description}</p>
              </div>
              <button
                onClick={() => runTest(test.key, async () => {
                  switch (test.key) {
                    case 'env': return await invoke('load_settings');
                    case 'gemini': return await invoke('test_gemini_api');
                    case 'mongodb': return await invoke('test_mongodb_connection');
                    case 'ai_request': return await invoke('process_ai_request', { 
                      prompt: 'System test: Please respond with "System test successful" to confirm AI processing is working.' 
                    });
                    default: throw new Error('Unknown test');
                  }
                })}
                disabled={isRunning}
                style={{
                  marginLeft: 'auto',
                  background: 'rgba(255, 255, 255, 0.1)',
                  color: 'white',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  padding: '5px 10px',
                  borderRadius: '3px',
                  cursor: isRunning ? 'not-allowed' : 'pointer',
                  fontSize: '12px'
                }}
              >
                Test
              </button>
            </div>
            
            {testResults[test.key] && (
              <div style={{
                background: 'rgba(0, 0, 0, 0.3)',
                padding: '10px',
                borderRadius: '5px',
                fontSize: '12px',
                whiteSpace: 'pre-wrap',
                maxHeight: '200px',
                overflow: 'auto'
              }}>
                {testResults[test.key].result}
              </div>
            )}
          </div>
        ))}
      </div>

      <div style={{ marginTop: '20px', fontSize: '12px', color: '#aaa' }}>
        <p>💡 This test suite verifies:</p>
        <ul style={{ margin: 0, paddingLeft: '20px' }}>
          <li>Environment variables are loaded correctly</li>
          <li>Gemini AI API key is valid and working</li>
          <li>MongoDB connection string is properly formatted</li>
          <li>End-to-end AI request processing</li>
        </ul>
      </div>
    </div>
  );
};

export default SystemTest;
