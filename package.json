{"name": "cluely-ai-assistant", "version": "0.1.0", "description": "A stealth AI assistant that provides real-time contextual suggestions", "main": "src/main.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "test": "vitest", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "format": "prettier --write src"}, "dependencies": {"@tauri-apps/api": "^1.5.3", "react": "^18.2.0", "react-dom": "^18.2.0", "mongodb": "^6.3.0", "axios": "^1.6.2", "zustand": "^4.4.7", "framer-motion": "^10.16.16", "react-hotkeys-hook": "^4.4.1", "tesseract.js": "^5.0.4", "web-speech-api": "^0.0.1"}, "devDependencies": {"@tauri-apps/cli": "^1.5.8", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "vite": "^5.0.8", "vitest": "^1.0.4", "eslint": "^8.55.0", "prettier": "^3.1.1", "typescript": "^5.3.3"}, "repository": {"type": "git", "url": "https://github.com/yourusername/cluely-ai-assistant.git"}, "keywords": ["ai", "assistant", "desktop", "real-time", "screen-capture", "speech-recognition"], "author": "Your Name", "license": "MIT", "engines": {"node": ">=18.0.0"}}