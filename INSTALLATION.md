# Installation Guide - Cluely AI Assistant

This guide will walk you through setting up <PERSON><PERSON>ly AI Assistant on your system.

## Quick Start

```bash
# Clone the repository
git clone https://github.com/yourusername/cluely-ai-assistant.git
cd cluely-ai-assistant

# Run the setup script
./setup.sh

# Configure your environment
cp .env.example .env
# Edit .env with your API keys

# Start development
npm run tauri:dev
```

## Prerequisites

### Required Software

1. **Node.js 18+**
   - Download from [nodejs.org](https://nodejs.org/)
   - Verify: `node --version`

2. **Rust 1.70+**
   - Install from [rustup.rs](https://rustup.rs/)
   - Verify: `rustc --version`

3. **MongoDB**
   - Local: [MongoDB Community Server](https://www.mongodb.com/try/download/community)
   - Cloud: [MongoDB Atlas](https://www.mongodb.com/atlas) (recommended)

### API Keys

1. **Gemini AI API Key**
   - Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Create a new project and generate an API key
   - Keep this key secure - you'll need it for configuration

## Platform-Specific Setup

### macOS

1. **Install Xcode Command Line Tools**
   ```bash
   xcode-select --install
   ```

2. **Install Homebrew** (if not already installed)
   ```bash
   /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
   ```

3. **Install MongoDB** (optional - you can use MongoDB Atlas)
   ```bash
   brew tap mongodb/brew
   brew install mongodb-community
   brew services start mongodb/brew/mongodb-community
   ```

4. **System Permissions**
   After installation, you'll need to grant permissions:
   - **Screen Recording**: System Preferences > Security & Privacy > Privacy > Screen Recording
   - **Microphone**: System Preferences > Security & Privacy > Privacy > Microphone
   - **Accessibility**: System Preferences > Security & Privacy > Privacy > Accessibility

### Windows

1. **Install Visual Studio Build Tools**
   - Download from [Microsoft](https://visualstudio.microsoft.com/visual-cpp-build-tools/)
   - Install "C++ build tools" workload

2. **Install MongoDB** (optional)
   - Download from [MongoDB](https://www.mongodb.com/try/download/community)
   - Follow the installation wizard
   - Start MongoDB service

3. **System Permissions**
   - **Microphone**: Windows Settings > Privacy > Microphone
   - **Screen capture**: Usually available by default

### Linux (Ubuntu/Debian)

1. **Install build dependencies**
   ```bash
   sudo apt update
   sudo apt install -y build-essential curl wget libssl-dev pkg-config
   ```

2. **Install additional dependencies**
   ```bash
   sudo apt install -y libgtk-3-dev libwebkit2gtk-4.0-dev libappindicator3-dev librsvg2-dev
   ```

3. **Install MongoDB** (optional)
   ```bash
   wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -
   echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
   sudo apt update
   sudo apt install -y mongodb-org
   sudo systemctl start mongod
   sudo systemctl enable mongod
   ```

## Step-by-Step Installation

### 1. Clone and Setup

```bash
# Clone the repository
git clone https://github.com/yourusername/cluely-ai-assistant.git
cd cluely-ai-assistant

# Make setup script executable
chmod +x setup.sh

# Run setup script
./setup.sh
```

### 2. Configure Environment

```bash
# Copy environment template
cp .env.example .env

# Edit the .env file
nano .env  # or use your preferred editor
```

Add your configuration:
```env
GEMINI_API_KEY=your_actual_api_key_here
MONGODB_URI=mongodb://localhost:27017/cluely
# ... other settings
```

### 3. Install Dependencies

```bash
# Install Node.js dependencies
npm install

# Install Rust dependencies
cd src-tauri
cargo build
cd ..
```

### 4. Install Tauri CLI

```bash
# Install Tauri CLI globally
cargo install tauri-cli

# Or install locally
npm install --save-dev @tauri-apps/cli
```

### 5. Database Setup

#### Option A: Local MongoDB
```bash
# Start MongoDB (if installed locally)
# macOS:
brew services start mongodb/brew/mongodb-community

# Linux:
sudo systemctl start mongod

# Windows:
net start MongoDB
```

#### Option B: MongoDB Atlas (Recommended)
1. Create account at [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create a new cluster
3. Get connection string
4. Update `MONGODB_URI` in `.env`

### 6. Test Installation

```bash
# Test frontend build
npm run build

# Test Rust backend
cd src-tauri
cargo check
cd ..

# Start development server
npm run tauri:dev
```

## Configuration

### Environment Variables

Edit `.env` file with your settings:

```env
# Required
GEMINI_API_KEY=your_gemini_api_key
MONGODB_URI=your_mongodb_connection_string

# Optional (with defaults)
CAPTURE_INTERVAL=2000
ENABLE_SCREEN_CAPTURE=true
ENABLE_AUDIO_CAPTURE=true
STEALTH_MODE=true
```

### Application Settings

On first run, configure the application:

1. **API Keys**: Enter your Gemini API key in settings
2. **Database**: Configure MongoDB connection
3. **Permissions**: Grant required system permissions
4. **Capture Settings**: Adjust capture intervals and options

## Troubleshooting

### Common Issues

#### 1. Permission Denied Errors
```bash
# Make sure setup script is executable
chmod +x setup.sh

# Check file permissions
ls -la setup.sh
```

#### 2. Node.js Version Issues
```bash
# Check Node.js version
node --version

# Update Node.js if needed
# Use nvm (recommended) or download from nodejs.org
```

#### 3. Rust Compilation Errors
```bash
# Update Rust
rustup update

# Clean and rebuild
cd src-tauri
cargo clean
cargo build
cd ..
```

#### 4. MongoDB Connection Issues
```bash
# Check if MongoDB is running
# macOS/Linux:
ps aux | grep mongod

# Windows:
tasklist | findstr mongod

# Test connection
mongosh  # or mongo (older versions)
```

#### 5. Tauri Build Errors
```bash
# Install Tauri CLI
cargo install tauri-cli

# Check Tauri dependencies
cd src-tauri
cargo check
cd ..
```

### Platform-Specific Issues

#### macOS
- **Gatekeeper Issues**: Right-click app and select "Open" for first run
- **Permission Dialogs**: Grant permissions when prompted
- **Xcode Issues**: Ensure Xcode Command Line Tools are installed

#### Windows
- **Antivirus**: Add project folder to antivirus exclusions
- **Windows Defender**: Allow app through Windows Defender
- **Build Tools**: Ensure Visual Studio Build Tools are installed

#### Linux
- **Missing Libraries**: Install development packages for your distribution
- **Wayland Issues**: Some features may require X11
- **Audio Issues**: Configure PulseAudio or ALSA properly

## Development Setup

### IDE Configuration

#### VS Code (Recommended)
Install these extensions:
- Rust Analyzer
- Tauri
- ES7+ React/Redux/React-Native snippets
- Prettier
- ESLint

#### Other IDEs
- **IntelliJ IDEA**: Install Rust plugin
- **Vim/Neovim**: Configure rust-analyzer LSP

### Development Commands

```bash
# Start development server
npm run tauri:dev

# Build for production
npm run tauri:build

# Run tests
npm test
cd src-tauri && cargo test

# Format code
npm run format
cd src-tauri && cargo fmt

# Lint code
npm run lint
cd src-tauri && cargo clippy
```

## Next Steps

After successful installation:

1. **Read the README**: Understand the application features
2. **Review Terms of Service**: Understand ethical use requirements
3. **Configure Settings**: Set up your preferences
4. **Test Features**: Try screen capture and AI suggestions
5. **Grant Permissions**: Ensure all system permissions are granted

## Getting Help

If you encounter issues:

1. **Check Logs**: Look for error messages in console
2. **Search Issues**: Check GitHub issues for similar problems
3. **Create Issue**: Report bugs with detailed information
4. **Join Community**: Join our Discord for real-time help

## Security Notes

- **API Keys**: Never commit API keys to version control
- **Permissions**: Only grant necessary system permissions
- **Data**: Understand what data is being captured and stored
- **Network**: Ensure secure connections to external services

---

**Remember**: This software captures screen and audio content. Use it responsibly and in compliance with all applicable laws and ethical guidelines.
