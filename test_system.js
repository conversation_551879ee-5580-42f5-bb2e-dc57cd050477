#!/usr/bin/env node

// Simple test script to verify system components
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧪 Cluely AI Assistant System Test\n');

// Test 1: Check .env file
console.log('1️⃣ Testing Environment Configuration...');
try {
  const envPath = path.join(__dirname, '.env');
  const envContent = fs.readFileSync(envPath, 'utf8');
  
  const hasGeminiKey = envContent.includes('GEMINI_API_KEY=') && !envContent.includes('GEMINI_API_KEY=""');
  const hasMongoUri = envContent.includes('MONGODB_URI=') && !envContent.includes('MONGODB_URI=""');
  
  console.log(`   ✅ .env file exists`);
  console.log(`   ${hasGeminiKey ? '✅' : '❌'} Gemini API key configured`);
  console.log(`   ${hasMongoUri ? '✅' : '❌'} MongoDB URI configured`);
  
  if (hasGeminiKey && hasMongoUri) {
    console.log('   🎉 Environment configuration: PASSED\n');
  } else {
    console.log('   ⚠️  Environment configuration: INCOMPLETE\n');
  }
} catch (error) {
  console.log(`   ❌ Error reading .env file: ${error.message}\n`);
}

// Test 2: Check project structure
console.log('2️⃣ Testing Project Structure...');
const requiredFiles = [
  'src/App.jsx',
  'src/components/OverlayUI.jsx',
  'src/components/SystemTest.jsx',
  'src-tauri/src/main.rs',
  'src-tauri/Cargo.toml',
  'package.json'
];

let structureOk = true;
requiredFiles.forEach(file => {
  const exists = fs.existsSync(path.join(__dirname, file));
  console.log(`   ${exists ? '✅' : '❌'} ${file}`);
  if (!exists) structureOk = false;
});

console.log(`   ${structureOk ? '🎉' : '⚠️'} Project structure: ${structureOk ? 'PASSED' : 'INCOMPLETE'}\n`);

// Test 3: Test Gemini API directly
console.log('3️⃣ Testing Gemini API Connection...');
const testGeminiAPI = () => {
  return new Promise((resolve) => {
    // Read API key from .env
    const envContent = fs.readFileSync('.env', 'utf8');
    const apiKeyMatch = envContent.match(/GEMINI_API_KEY=(.+)/);
    
    if (!apiKeyMatch || !apiKeyMatch[1]) {
      resolve('❌ No API key found in .env file');
      return;
    }
    
    const apiKey = apiKeyMatch[1].trim();
    const testPayload = JSON.stringify({
      contents: [{
        parts: [{
          text: "Hello! This is a system test. Please respond with 'System test successful' to confirm the API is working."
        }]
      }]
    });
    
    const curlCommand = `curl -s -H "Content-Type: application/json" -d '${testPayload}' "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}"`;
    
    exec(curlCommand, (error, stdout, stderr) => {
      if (error) {
        resolve(`❌ API request failed: ${error.message}`);
        return;
      }
      
      try {
        const response = JSON.parse(stdout);
        if (response.error) {
          resolve(`❌ API error: ${response.error.message}`);
        } else if (response.candidates && response.candidates[0]) {
          const text = response.candidates[0].content.parts[0].text;
          resolve(`✅ API response: ${text.substring(0, 100)}...`);
        } else {
          resolve(`⚠️ Unexpected API response format`);
        }
      } catch (parseError) {
        resolve(`❌ Failed to parse API response: ${parseError.message}`);
      }
    });
  });
};

testGeminiAPI().then(result => {
  console.log(`   ${result}`);
  console.log(`   ${result.startsWith('✅') ? '🎉' : '⚠️'} Gemini API test: ${result.startsWith('✅') ? 'PASSED' : 'FAILED'}\n`);
  
  // Test 4: MongoDB URI validation
  console.log('4️⃣ Testing MongoDB URI Format...');
  try {
    const envContent = fs.readFileSync('.env', 'utf8');
    const mongoUriMatch = envContent.match(/MONGODB_URI="?([^"\n]+)"?/);
    
    if (!mongoUriMatch || !mongoUriMatch[1]) {
      console.log('   ❌ No MongoDB URI found in .env file');
      console.log('   ⚠️ MongoDB URI test: FAILED\n');
    } else {
      const uri = mongoUriMatch[1].trim();
      const isValidFormat = (uri.startsWith('mongodb://') || uri.startsWith('mongodb+srv://')) && 
                           uri.includes('@') && uri.includes('.');
      
      console.log(`   ${isValidFormat ? '✅' : '❌'} URI format validation`);
      console.log(`   📝 URI: ${uri.substring(0, 50)}...`);
      console.log(`   ${isValidFormat ? '🎉' : '⚠️'} MongoDB URI test: ${isValidFormat ? 'PASSED' : 'FAILED'}\n`);
    }
  } catch (error) {
    console.log(`   ❌ Error validating MongoDB URI: ${error.message}\n`);
  }
  
  // Summary
  console.log('📊 Test Summary:');
  console.log('   • Environment: Check .env file manually');
  console.log('   • Project Structure: Check file listing above');
  console.log('   • Gemini API: Check API response above');
  console.log('   • MongoDB URI: Check format validation above');
  console.log('\n🚀 To run the full interactive test, click the "🧪 System Test" button in the application!');
});
