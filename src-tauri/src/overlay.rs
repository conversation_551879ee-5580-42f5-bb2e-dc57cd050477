use tauri::{Window, Manager, AppHandle};
use std::error::Error;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct OverlayConfig {
    pub visible: bool,
    pub opacity: f64,
    pub always_on_top: bool,
    pub skip_taskbar: bool,
    pub decorations: bool,
    pub resizable: bool,
    pub position: OverlayPosition,
    pub size: OverlaySize,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OverlayPosition {
    pub x: i32,
    pub y: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OverlaySize {
    pub width: u32,
    pub height: u32,
}

pub struct OverlayManager {
    app_handle: AppHandle,
    config: OverlayConfig,
}

impl OverlayManager {
    pub fn new(app_handle: AppHandle) -> Self {
        let config = OverlayConfig {
            visible: false,
            opacity: 0.85,
            always_on_top: true,
            skip_taskbar: true,
            decorations: false,
            resizable: true,
            position: OverlayPosition { x: 100, y: 100 },
            size: OverlaySize { width: 400, height: 600 },
        };

        OverlayManager {
            app_handle,
            config,
        }
    }

    pub async fn show_overlay(&mut self) -> Result<(), Box<dyn Error>> {
        if let Some(window) = self.app_handle.get_window("main") {
            // Configure window for overlay mode
            window.set_always_on_top(self.config.always_on_top)?;
            window.set_skip_taskbar(self.config.skip_taskbar)?;
            window.set_decorations(self.config.decorations)?;
            window.set_resizable(self.config.resizable)?;
            
            // Set position and size
            window.set_position(tauri::Position::Physical(tauri::PhysicalPosition {
                x: self.config.position.x,
                y: self.config.position.y,
            }))?;
            
            window.set_size(tauri::Size::Physical(tauri::PhysicalSize {
                width: self.config.size.width,
                height: self.config.size.height,
            }))?;

            // Show the window
            window.show()?;
            window.set_focus()?;
            
            self.config.visible = true;
            
            // Apply stealth mode configurations
            self.apply_stealth_mode(&window).await?;
        }

        Ok(())
    }

    pub async fn hide_overlay(&mut self) -> Result<(), Box<dyn Error>> {
        if let Some(window) = self.app_handle.get_window("main") {
            window.hide()?;
            self.config.visible = false;
        }
        Ok(())
    }

    pub async fn toggle_overlay(&mut self) -> Result<bool, Box<dyn Error>> {
        if self.config.visible {
            self.hide_overlay().await?;
        } else {
            self.show_overlay().await?;
        }
        Ok(self.config.visible)
    }

    pub async fn set_opacity(&mut self, opacity: f64) -> Result<(), Box<dyn Error>> {
        self.config.opacity = opacity.clamp(0.1, 1.0);
        
        if let Some(window) = self.app_handle.get_window("main") {
            // Note: Tauri doesn't have direct opacity control
            // This would need to be implemented via CSS or platform-specific code
            window.emit("set-opacity", self.config.opacity)?;
        }
        
        Ok(())
    }

    pub async fn set_position(&mut self, x: i32, y: i32) -> Result<(), Box<dyn Error>> {
        self.config.position = OverlayPosition { x, y };
        
        if let Some(window) = self.app_handle.get_window("main") {
            window.set_position(tauri::Position::Physical(tauri::PhysicalPosition { x, y }))?;
        }
        
        Ok(())
    }

    pub async fn set_size(&mut self, width: u32, height: u32) -> Result<(), Box<dyn Error>> {
        self.config.size = OverlaySize { width, height };
        
        if let Some(window) = self.app_handle.get_window("main") {
            window.set_size(tauri::Size::Physical(tauri::PhysicalSize { width, height }))?;
        }
        
        Ok(())
    }

    async fn apply_stealth_mode(&self, window: &Window) -> Result<(), Box<dyn Error>> {
        // Platform-specific stealth mode implementations
        
        #[cfg(target_os = "macos")]
        {
            self.apply_macos_stealth_mode(window).await?;
        }
        
        #[cfg(target_os = "windows")]
        {
            self.apply_windows_stealth_mode(window).await?;
        }
        
        #[cfg(target_os = "linux")]
        {
            self.apply_linux_stealth_mode(window).await?;
        }
        
        Ok(())
    }

    #[cfg(target_os = "macos")]
    async fn apply_macos_stealth_mode(&self, window: &Window) -> Result<(), Box<dyn Error>> {
        use cocoa::appkit::{NSWindow, NSWindowSharingType};
        use cocoa::base::id;
        
        // Get the native window handle
        let ns_window = window.ns_window()? as id;
        
        unsafe {
            // Prevent the window from being captured in screenshots or screen recordings
            ns_window.setSharingType_(NSWindowSharingType::NSWindowSharingNone);
            
            // Set window level to be above most other windows but below screen savers
            ns_window.setLevel_(25); // kCGMaximumWindowLevel - 1
            
            // Make window non-activating (doesn't steal focus)
            ns_window.setCanHide_(false);
        }
        
        Ok(())
    }

    #[cfg(target_os = "windows")]
    async fn apply_windows_stealth_mode(&self, window: &Window) -> Result<(), Box<dyn Error>> {
        use winapi::um::winuser::{SetWindowDisplayAffinity, WDA_EXCLUDEFROMCAPTURE};
        
        // Get the native window handle
        let hwnd = window.hwnd()?;
        
        unsafe {
            // Exclude window from screen capture
            SetWindowDisplayAffinity(hwnd.0 as _, WDA_EXCLUDEFROMCAPTURE);
        }
        
        Ok(())
    }

    #[cfg(target_os = "linux")]
    async fn apply_linux_stealth_mode(&self, _window: &Window) -> Result<(), Box<dyn Error>> {
        // Linux stealth mode implementation would depend on the window manager
        // For now, just log that it's not implemented
        println!("Linux stealth mode not yet implemented");
        Ok(())
    }

    pub async fn update_content(&self, content: &str) -> Result<(), Box<dyn Error>> {
        if let Some(window) = self.app_handle.get_window("main") {
            window.emit("update-content", content)?;
        }
        Ok(())
    }

    pub async fn show_notification(&self, message: &str, duration_ms: u64) -> Result<(), Box<dyn Error>> {
        if let Some(window) = self.app_handle.get_window("main") {
            let notification = serde_json::json!({
                "message": message,
                "duration": duration_ms
            });
            window.emit("show-notification", notification)?;
        }
        Ok(())
    }

    pub async fn set_theme(&self, theme: &str) -> Result<(), Box<dyn Error>> {
        if let Some(window) = self.app_handle.get_window("main") {
            window.emit("set-theme", theme)?;
        }
        Ok(())
    }

    pub fn get_config(&self) -> &OverlayConfig {
        &self.config
    }

    pub async fn save_config(&self) -> Result<(), Box<dyn Error>> {
        // Save overlay configuration to local storage or database
        let config_json = serde_json::to_string(&self.config)?;
        
        // For now, just emit to frontend to save in localStorage
        if let Some(window) = self.app_handle.get_window("main") {
            window.emit("save-overlay-config", config_json)?;
        }
        
        Ok(())
    }

    pub async fn load_config(&mut self) -> Result<(), Box<dyn Error>> {
        // Load overlay configuration from local storage or database
        // For now, this would be handled by the frontend
        
        if let Some(window) = self.app_handle.get_window("main") {
            window.emit("load-overlay-config", {})?;
        }
        
        Ok(())
    }

    pub async fn handle_screen_edge(&mut self, edge: &str) -> Result<(), Box<dyn Error>> {
        // Handle window snapping to screen edges
        match edge {
            "left" => self.set_position(0, self.config.position.y).await?,
            "right" => {
                // Get screen width and position window at right edge
                // This would need screen dimension detection
                self.set_position(1920 - self.config.size.width as i32, self.config.position.y).await?;
            },
            "top" => self.set_position(self.config.position.x, 0).await?,
            "bottom" => {
                // Get screen height and position window at bottom edge
                // This would need screen dimension detection
                self.set_position(self.config.position.x, 1080 - self.config.size.height as i32).await?;
            },
            _ => {}
        }
        
        Ok(())
    }

    pub async fn set_click_through(&self, enabled: bool) -> Result<(), Box<dyn Error>> {
        // Enable/disable click-through mode
        if let Some(window) = self.app_handle.get_window("main") {
            window.emit("set-click-through", enabled)?;
        }
        Ok(())
    }
}

// Tauri commands for overlay management
#[tauri::command]
pub async fn show_overlay(app_handle: AppHandle) -> Result<(), String> {
    let mut overlay = OverlayManager::new(app_handle);
    overlay.show_overlay().await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn hide_overlay(app_handle: AppHandle) -> Result<(), String> {
    let mut overlay = OverlayManager::new(app_handle);
    overlay.hide_overlay().await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn toggle_overlay(app_handle: AppHandle) -> Result<bool, String> {
    let mut overlay = OverlayManager::new(app_handle);
    overlay.toggle_overlay().await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn set_overlay_opacity(app_handle: AppHandle, opacity: f64) -> Result<(), String> {
    let mut overlay = OverlayManager::new(app_handle);
    overlay.set_opacity(opacity).await.map_err(|e| e.to_string())
}
