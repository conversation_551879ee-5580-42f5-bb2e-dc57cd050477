use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::error::Error;
use std::collections::HashMap;
use tokio::sync::Mutex;
use std::sync::Arc;

#[derive(Debug, Serialize, Deserialize)]
pub struct AIRequest {
    pub context_type: String, // "screen", "audio", "combined"
    pub screen_text: Option<String>,
    pub audio_transcript: Option<String>,
    pub image_data: Option<String>,
    pub user_query: Option<String>,
    pub session_history: Vec<ContextItem>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ContextItem {
    pub timestamp: String,
    pub content_type: String,
    pub content: String,
    pub metadata: HashMap<String, String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AIResponse {
    pub suggestion: String,
    pub confidence: f32,
    pub action_items: Vec<String>,
    pub follow_up_questions: Vec<String>,
    pub summary: Option<String>,
    pub processing_time_ms: u64,
}

#[derive(Debug, Serialize, Deserialize)]
struct GeminiRequest {
    contents: Vec<GeminiContent>,
    generation_config: GeminiGenerationConfig,
}

#[derive(Debug, Serialize, Deserialize)]
struct GeminiContent {
    parts: Vec<GeminiPart>,
}

#[derive(Debug, Serialize, Deserialize)]
struct GeminiPart {
    text: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct GeminiGenerationConfig {
    temperature: f32,
    top_k: i32,
    top_p: f32,
    max_output_tokens: i32,
}

#[derive(Debug, Serialize, Deserialize)]
struct GeminiResponse {
    candidates: Vec<GeminiCandidate>,
}

#[derive(Debug, Serialize, Deserialize)]
struct GeminiCandidate {
    content: GeminiContent,
}

pub struct AIIntegrationManager {
    client: Client,
    api_key: Arc<Mutex<Option<String>>>,
    context_history: Arc<Mutex<Vec<ContextItem>>>,
    max_context_items: usize,
}

impl AIIntegrationManager {
    pub fn new() -> Self {
        AIIntegrationManager {
            client: Client::new(),
            api_key: Arc::new(Mutex::new(None)),
            context_history: Arc::new(Mutex::new(Vec::new())),
            max_context_items: 50, // Keep last 50 context items
        }
    }

    pub async fn set_api_key(&self, key: String) {
        let mut api_key = self.api_key.lock().await;
        *api_key = Some(key);
    }

    pub async fn process_context(&self, context: String) -> Result<String, Box<dyn Error>> {
        let start_time = std::time::Instant::now();
        
        // Parse the context (this would be more sophisticated in practice)
        let ai_request = self.parse_context(context).await?;
        
        // Send to Gemini AI
        let response = self.send_to_gemini(ai_request).await?;
        
        let processing_time = start_time.elapsed().as_millis() as u64;
        
        // Add to context history
        self.add_to_context_history(ContextItem {
            timestamp: chrono::Utc::now().to_rfc3339(),
            content_type: "ai_response".to_string(),
            content: response.suggestion.clone(),
            metadata: {
                let mut map = HashMap::new();
                map.insert("confidence".to_string(), response.confidence.to_string());
                map.insert("processing_time_ms".to_string(), processing_time.to_string());
                map
            },
        }).await;

        Ok(response.suggestion)
    }

    async fn parse_context(&self, context: String) -> Result<AIRequest, Box<dyn Error>> {
        // This would parse the incoming context data
        // For now, treat it as a simple text query
        
        let history = self.context_history.lock().await.clone();
        
        Ok(AIRequest {
            context_type: "text".to_string(),
            screen_text: Some(context),
            audio_transcript: None,
            image_data: None,
            user_query: None,
            session_history: history,
        })
    }

    async fn send_to_gemini(&self, request: AIRequest) -> Result<AIResponse, Box<dyn Error>> {
        let api_key = self.api_key.lock().await;
        let api_key = api_key.as_ref().ok_or("Gemini API key not set")?;

        // Build the prompt
        let prompt = self.build_prompt(&request)?;
        
        let gemini_request = GeminiRequest {
            contents: vec![GeminiContent {
                parts: vec![GeminiPart {
                    text: prompt,
                }],
            }],
            generation_config: GeminiGenerationConfig {
                temperature: 0.7,
                top_k: 40,
                top_p: 0.95,
                max_output_tokens: 1024,
            },
        };

        let url = format!(
            "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={}",
            api_key
        );

        let response = self.client
            .post(&url)
            .json(&gemini_request)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(format!("Gemini API error: {}", error_text).into());
        }

        let gemini_response: GeminiResponse = response.json().await?;
        
        let suggestion = gemini_response
            .candidates
            .first()
            .and_then(|c| c.content.parts.first())
            .map(|p| p.text.clone())
            .unwrap_or_else(|| "No response generated".to_string());

        Ok(AIResponse {
            suggestion,
            confidence: 0.8, // TODO: Calculate actual confidence
            action_items: self.extract_action_items(&suggestion),
            follow_up_questions: self.extract_follow_up_questions(&suggestion),
            summary: None,
            processing_time_ms: 0, // Will be set by caller
        })
    }

    fn build_prompt(&self, request: &AIRequest) -> Result<String, Box<dyn Error>> {
        let mut prompt = String::new();
        
        prompt.push_str("You are Cluely, an AI assistant that provides real-time contextual suggestions based on what the user is seeing and hearing. ");
        prompt.push_str("Analyze the provided context and give helpful, actionable suggestions.\n\n");

        if let Some(screen_text) = &request.screen_text {
            prompt.push_str("SCREEN CONTENT:\n");
            prompt.push_str(screen_text);
            prompt.push_str("\n\n");
        }

        if let Some(audio_transcript) = &request.audio_transcript {
            prompt.push_str("AUDIO TRANSCRIPT:\n");
            prompt.push_str(audio_transcript);
            prompt.push_str("\n\n");
        }

        if let Some(user_query) = &request.user_query {
            prompt.push_str("USER QUERY:\n");
            prompt.push_str(user_query);
            prompt.push_str("\n\n");
        }

        // Add recent context history
        if !request.session_history.is_empty() {
            prompt.push_str("RECENT CONTEXT:\n");
            for item in request.session_history.iter().rev().take(5) {
                prompt.push_str(&format!("- {}: {}\n", item.content_type, item.content));
            }
            prompt.push_str("\n");
        }

        prompt.push_str("Please provide:\n");
        prompt.push_str("1. A helpful suggestion or insight based on the context\n");
        prompt.push_str("2. Any action items the user should consider\n");
        prompt.push_str("3. Follow-up questions that might be relevant\n\n");
        prompt.push_str("Keep your response concise and actionable. Focus on what would be most helpful right now.");

        Ok(prompt)
    }

    fn extract_action_items(&self, text: &str) -> Vec<String> {
        // Simple extraction - look for numbered lists or bullet points
        text.lines()
            .filter(|line| {
                line.trim_start().starts_with("- ") || 
                line.trim_start().chars().next().map_or(false, |c| c.is_ascii_digit())
            })
            .map(|line| line.trim().to_string())
            .collect()
    }

    fn extract_follow_up_questions(&self, text: &str) -> Vec<String> {
        // Simple extraction - look for lines ending with question marks
        text.lines()
            .filter(|line| line.trim().ends_with('?'))
            .map(|line| line.trim().to_string())
            .collect()
    }

    async fn add_to_context_history(&self, item: ContextItem) {
        let mut history = self.context_history.lock().await;
        history.push(item);
        
        // Keep only the most recent items
        if history.len() > self.max_context_items {
            history.drain(0..history.len() - self.max_context_items);
        }
    }

    pub async fn process_screen_capture(&self, capture: &crate::screen_capture::ScreenCapture) -> Result<AIResponse, Box<dyn Error>> {
        let request = AIRequest {
            context_type: "screen".to_string(),
            screen_text: Some(capture.extracted_text.clone()),
            audio_transcript: None,
            image_data: Some(capture.image_data.clone()),
            user_query: None,
            session_history: self.context_history.lock().await.clone(),
        };

        self.send_to_gemini(request).await
    }

    pub async fn process_audio_capture(&self, capture: &crate::audio_capture::AudioCapture) -> Result<AIResponse, Box<dyn Error>> {
        let request = AIRequest {
            context_type: "audio".to_string(),
            screen_text: None,
            audio_transcript: Some(capture.transcription.clone()),
            image_data: None,
            user_query: None,
            session_history: self.context_history.lock().await.clone(),
        };

        self.send_to_gemini(request).await
    }

    pub async fn process_combined_context(
        &self,
        screen_capture: &crate::screen_capture::ScreenCapture,
        audio_capture: &crate::audio_capture::AudioCapture,
    ) -> Result<AIResponse, Box<dyn Error>> {
        let request = AIRequest {
            context_type: "combined".to_string(),
            screen_text: Some(screen_capture.extracted_text.clone()),
            audio_transcript: Some(audio_capture.transcription.clone()),
            image_data: Some(screen_capture.image_data.clone()),
            user_query: None,
            session_history: self.context_history.lock().await.clone(),
        };

        self.send_to_gemini(request).await
    }

    pub async fn clear_context_history(&self) {
        let mut history = self.context_history.lock().await;
        history.clear();
    }

    pub async fn get_context_summary(&self) -> Result<String, Box<dyn Error>> {
        let history = self.context_history.lock().await;
        
        if history.is_empty() {
            return Ok("No context available".to_string());
        }

        // Create a summary request
        let context_text = history.iter()
            .map(|item| format!("{}: {}", item.content_type, item.content))
            .collect::<Vec<_>>()
            .join("\n");

        let request = AIRequest {
            context_type: "summary".to_string(),
            screen_text: Some(context_text),
            audio_transcript: None,
            image_data: None,
            user_query: Some("Please provide a summary of this session".to_string()),
            session_history: vec![],
        };

        let response = self.send_to_gemini(request).await?;
        Ok(response.suggestion)
    }
}

// Global instance
static mut AI_INTEGRATION_MANAGER: Option<AIIntegrationManager> = None;
static INIT: std::sync::Once = std::sync::Once::new();

pub async fn process_context(context: String) -> Result<String, Box<dyn Error>> {
    INIT.call_once(|| {
        unsafe {
            AI_INTEGRATION_MANAGER = Some(AIIntegrationManager::new());
        }
    });

    unsafe {
        if let Some(manager) = &AI_INTEGRATION_MANAGER {
            manager.process_context(context).await
        } else {
            Err("Failed to initialize AI integration manager".into())
        }
    }
}

pub async fn set_api_key(key: String) -> Result<(), Box<dyn Error>> {
    unsafe {
        if let Some(manager) = &AI_INTEGRATION_MANAGER {
            manager.set_api_key(key).await;
            Ok(())
        } else {
            Err("AI integration manager not initialized".into())
        }
    }
}
