use cpal::{Device, Host, Stream, StreamConfig, SupportedStreamConfig};
use cpal::traits::{DeviceTrait, HostTrait, StreamTrait};
use std::sync::{Arc, Mutex};
use std::error::Error;
use hound::{WavWriter, WavSpec};
use std::io::Cursor;
use serde::{Deserialize, Serialize};
use base64::{Engine as _, engine::general_purpose};

#[derive(Debug, Serialize, Deserialize)]
pub struct AudioCapture {
    pub timestamp: String,
    pub audio_data: String, // Base64 encoded WAV data
    pub transcription: String,
    pub duration_ms: u64,
    pub sample_rate: u32,
    pub channels: u16,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AudioDevice {
    pub name: String,
    pub is_input: bool,
    pub is_default: bool,
    pub supported_configs: Vec<String>,
}

pub struct AudioCaptureManager {
    input_device: Option<Device>,
    output_device: Option<Device>,
    input_stream: Option<Stream>,
    output_stream: Option<Stream>,
    capture_active: Arc<Mutex<bool>>,
    audio_buffer: Arc<Mutex<Vec<f32>>>,
    sample_rate: u32,
    channels: u16,
}

impl AudioCaptureManager {
    pub fn new() -> Result<Self, Box<dyn Error>> {
        let host = cpal::default_host();
        
        // Get default input and output devices
        let input_device = host.default_input_device();
        let output_device = host.default_output_device();

        Ok(AudioCaptureManager {
            input_device,
            output_device,
            input_stream: None,
            output_stream: None,
            capture_active: Arc::new(Mutex::new(false)),
            audio_buffer: Arc::new(Mutex::new(Vec::new())),
            sample_rate: 44100,
            channels: 1,
        })
    }

    pub async fn start_capture(&mut self) -> Result<String, Box<dyn Error>> {
        let mut active = self.capture_active.lock().unwrap();
        *active = true;
        drop(active);

        // Start microphone capture
        self.start_microphone_capture().await?;
        
        // Start system audio capture (if available)
        self.start_system_audio_capture().await?;

        // Start processing task
        self.start_processing_task().await;

        Ok("Audio capture started".to_string())
    }

    pub async fn stop_capture(&mut self) -> Result<String, Box<dyn Error>> {
        let mut active = self.capture_active.lock().unwrap();
        *active = false;
        drop(active);

        // Stop streams
        if let Some(stream) = self.input_stream.take() {
            drop(stream);
        }
        if let Some(stream) = self.output_stream.take() {
            drop(stream);
        }

        Ok("Audio capture stopped".to_string())
    }

    async fn start_microphone_capture(&mut self) -> Result<(), Box<dyn Error>> {
        if let Some(device) = &self.input_device {
            let config = device.default_input_config()?;
            self.sample_rate = config.sample_rate().0;
            self.channels = config.channels();

            let audio_buffer = Arc::clone(&self.audio_buffer);
            let capture_active = Arc::clone(&self.capture_active);

            let stream = match config.sample_format() {
                cpal::SampleFormat::F32 => {
                    device.build_input_stream(
                        &config.into(),
                        move |data: &[f32], _: &cpal::InputCallbackInfo| {
                            if *capture_active.lock().unwrap() {
                                let mut buffer = audio_buffer.lock().unwrap();
                                buffer.extend_from_slice(data);
                            }
                        },
                        |err| eprintln!("Audio input error: {}", err),
                        None,
                    )?
                }
                cpal::SampleFormat::I16 => {
                    device.build_input_stream(
                        &config.into(),
                        move |data: &[i16], _: &cpal::InputCallbackInfo| {
                            if *capture_active.lock().unwrap() {
                                let mut buffer = audio_buffer.lock().unwrap();
                                // Convert i16 to f32
                                let f32_data: Vec<f32> = data.iter()
                                    .map(|&sample| sample as f32 / i16::MAX as f32)
                                    .collect();
                                buffer.extend_from_slice(&f32_data);
                            }
                        },
                        |err| eprintln!("Audio input error: {}", err),
                        None,
                    )?
                }
                _ => return Err("Unsupported sample format".into()),
            };

            stream.play()?;
            self.input_stream = Some(stream);
        }

        Ok(())
    }

    async fn start_system_audio_capture(&mut self) -> Result<(), Box<dyn Error>> {
        // System audio capture is more complex and platform-specific
        // For now, we'll focus on microphone capture
        // TODO: Implement system audio capture for different platforms
        
        #[cfg(target_os = "macos")]
        {
            // macOS: Use Core Audio to capture system audio
            // This requires additional permissions and setup
        }
        
        #[cfg(target_os = "windows")]
        {
            // Windows: Use WASAPI loopback mode
            // This requires additional setup
        }
        
        #[cfg(target_os = "linux")]
        {
            // Linux: Use PulseAudio or ALSA
            // This requires additional setup
        }

        Ok(())
    }

    async fn start_processing_task(&self) {
        let audio_buffer = Arc::clone(&self.audio_buffer);
        let capture_active = Arc::clone(&self.capture_active);
        let sample_rate = self.sample_rate;
        let channels = self.channels;

        tokio::spawn(async move {
            while *capture_active.lock().unwrap() {
                // Process audio buffer every 5 seconds
                tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                
                let mut buffer = audio_buffer.lock().unwrap();
                if !buffer.is_empty() {
                    let audio_data = buffer.clone();
                    buffer.clear();
                    drop(buffer);

                    if let Err(e) = Self::process_audio_chunk(audio_data, sample_rate, channels).await {
                        eprintln!("Audio processing error: {}", e);
                    }
                }
            }
        });
    }

    async fn process_audio_chunk(
        audio_data: Vec<f32>,
        sample_rate: u32,
        channels: u16,
    ) -> Result<AudioCapture, Box<dyn Error>> {
        // Convert audio data to WAV format
        let wav_data = Self::convert_to_wav(&audio_data, sample_rate, channels)?;
        
        // Encode to base64
        let audio_data_b64 = general_purpose::STANDARD.encode(&wav_data);
        
        // Transcribe audio (placeholder - would use Whisper or similar)
        let transcription = Self::transcribe_audio(&wav_data).await?;
        
        let capture = AudioCapture {
            timestamp: chrono::Utc::now().to_rfc3339(),
            audio_data: audio_data_b64,
            transcription,
            duration_ms: (audio_data.len() as u64 * 1000) / (sample_rate as u64 * channels as u64),
            sample_rate,
            channels,
        };

        // Send to AI processing pipeline
        Self::send_to_ai_pipeline(&capture).await?;

        Ok(capture)
    }

    fn convert_to_wav(
        audio_data: &[f32],
        sample_rate: u32,
        channels: u16,
    ) -> Result<Vec<u8>, Box<dyn Error>> {
        let mut cursor = Cursor::new(Vec::new());
        
        let spec = WavSpec {
            channels,
            sample_rate,
            bits_per_sample: 16,
            sample_format: hound::SampleFormat::Int,
        };

        let mut writer = WavWriter::new(&mut cursor, spec)?;
        
        for &sample in audio_data {
            let sample_i16 = (sample * i16::MAX as f32) as i16;
            writer.write_sample(sample_i16)?;
        }
        
        writer.finalize()?;
        Ok(cursor.into_inner())
    }

    async fn transcribe_audio(wav_data: &[u8]) -> Result<String, Box<dyn Error>> {
        // TODO: Implement actual speech-to-text
        // This could use:
        // - OpenAI Whisper API
        // - Local Whisper model
        // - Google Speech-to-Text
        // - Azure Speech Services
        
        // For now, return a placeholder
        Ok("Audio transcription would appear here".to_string())
    }

    async fn send_to_ai_pipeline(capture: &AudioCapture) -> Result<(), Box<dyn Error>> {
        // Send to AI integration module
        println!("Captured audio: {} characters transcribed", capture.transcription.len());
        
        // TODO: Send to AI integration module
        // crate::ai_integration::process_audio_capture(capture).await?;
        
        Ok(())
    }

    pub async fn get_audio_devices() -> Result<Vec<AudioDevice>, Box<dyn Error>> {
        let host = cpal::default_host();
        let mut devices = Vec::new();

        // Input devices
        for device in host.input_devices()? {
            let name = device.name().unwrap_or_else(|_| "Unknown Device".to_string());
            let supported_configs: Vec<String> = device
                .supported_input_configs()?
                .map(|config| format!("{:?}", config))
                .collect();

            devices.push(AudioDevice {
                name,
                is_input: true,
                is_default: false, // TODO: Check if default
                supported_configs,
            });
        }

        // Output devices
        for device in host.output_devices()? {
            let name = device.name().unwrap_or_else(|_| "Unknown Device".to_string());
            let supported_configs: Vec<String> = device
                .supported_output_configs()?
                .map(|config| format!("{:?}", config))
                .collect();

            devices.push(AudioDevice {
                name,
                is_input: false,
                is_default: false, // TODO: Check if default
                supported_configs,
            });
        }

        Ok(devices)
    }
}

// Global instance
static mut AUDIO_CAPTURE_MANAGER: Option<AudioCaptureManager> = None;
static INIT: std::sync::Once = std::sync::Once::new();

pub async fn start_capture() -> Result<String, Box<dyn Error>> {
    INIT.call_once(|| {
        unsafe {
            AUDIO_CAPTURE_MANAGER = Some(AudioCaptureManager::new().unwrap());
        }
    });

    unsafe {
        if let Some(manager) = &mut AUDIO_CAPTURE_MANAGER {
            manager.start_capture().await
        } else {
            Err("Failed to initialize audio capture manager".into())
        }
    }
}

pub async fn stop_capture() -> Result<String, Box<dyn Error>> {
    unsafe {
        if let Some(manager) = &mut AUDIO_CAPTURE_MANAGER {
            manager.stop_capture().await
        } else {
            Err("Audio capture manager not initialized".into())
        }
    }
}
