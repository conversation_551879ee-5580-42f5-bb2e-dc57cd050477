// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri::{
    CustomMenuItem, Manager, SystemTray, SystemTrayEvent, SystemTrayMenu, SystemTrayMenuItem,
    GlobalShortcutManager
};
use std::sync::Mutex;

// mod screen_capture;
// mod audio_capture;
mod ai_integration;
// mod database;
// mod overlay;

// Global state for the application
struct AppState {
    is_listening: Mutex<bool>,
    current_session: Mutex<Option<String>>,
}

// Tauri commands that can be called from the frontend
#[tauri::command]
async fn toggle_listening(state: tauri::State<'_, AppState>) -> Result<bool, String> {
    let mut is_listening = state.is_listening.lock().unwrap();
    *is_listening = !*is_listening;
    Ok(*is_listening)
}

#[tauri::command]
async fn start_screen_capture() -> Result<String, String> {
    // TODO: Implement screen capture
    Ok("Screen capture started (placeholder)".to_string())
}

#[tauri::command]
async fn start_audio_capture() -> Result<String, String> {
    // TODO: Implement audio capture
    Ok("Audio capture started (placeholder)".to_string())
}

#[tauri::command]
async fn send_to_ai(context: String) -> Result<String, String> {
    ai_integration::process_context(context).await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn save_session_data(session_id: String, data: String) -> Result<(), String> {
    // TODO: Implement database save
    println!("Saving session data: {} - {}", session_id, data);
    Ok(())
}

fn create_system_tray() -> SystemTray {
    let quit = CustomMenuItem::new("quit".to_string(), "Quit");
    let show = CustomMenuItem::new("show".to_string(), "Show");
    let hide = CustomMenuItem::new("hide".to_string(), "Hide");
    let toggle_listening = CustomMenuItem::new("toggle_listening".to_string(), "Toggle Listening");
    
    let tray_menu = SystemTrayMenu::new()
        .add_item(show)
        .add_item(hide)
        .add_native_item(SystemTrayMenuItem::Separator)
        .add_item(toggle_listening)
        .add_native_item(SystemTrayMenuItem::Separator)
        .add_item(quit);
    
    SystemTray::new().with_menu(tray_menu)
}

fn setup_global_shortcuts(app: &tauri::App) -> Result<(), Box<dyn std::error::Error>> {
    let mut shortcut_manager = app.global_shortcut_manager();
    
    // Toggle overlay shortcut (Cmd+Shift+A on macOS, Ctrl+Shift+A on others)
    #[cfg(target_os = "macos")]
    let toggle_shortcut = "Cmd+Shift+A";
    #[cfg(not(target_os = "macos"))]
    let toggle_shortcut = "Ctrl+Shift+A";
    
    let app_handle = app.handle();
    shortcut_manager.register(toggle_shortcut, move || {
        if let Some(window) = app_handle.get_window("main") {
            let _ = window.emit("toggle-overlay", {});
        }
    })?;
    
    // Quick listen shortcut
    #[cfg(target_os = "macos")]
    let listen_shortcut = "Cmd+Shift+L";
    #[cfg(not(target_os = "macos"))]
    let listen_shortcut = "Ctrl+Shift+L";
    
    let app_handle = app.handle();
    shortcut_manager.register(listen_shortcut, move || {
        if let Some(window) = app_handle.get_window("main") {
            let _ = window.emit("quick-listen", {});
        }
    })?;
    
    Ok(())
}

fn main() {
    let app_state = AppState {
        is_listening: Mutex::new(false),
        current_session: Mutex::new(None),
    };
    
    tauri::Builder::default()
        .manage(app_state)
        .system_tray(create_system_tray())
        .on_system_tray_event(|app, event| match event {
            SystemTrayEvent::LeftClick {
                position: _,
                size: _,
                ..
            } => {
                // Do nothing on left click to maintain stealth
            }
            SystemTrayEvent::RightClick {
                position: _,
                size: _,
                ..
            } => {
                // Show context menu on right click
            }
            SystemTrayEvent::MenuItemClick { id, .. } => {
                match id.as_str() {
                    "quit" => {
                        std::process::exit(0);
                    }
                    "show" => {
                        if let Some(window) = app.get_window("main") {
                            let _ = window.show();
                            let _ = window.set_focus();
                        }
                    }
                    "hide" => {
                        if let Some(window) = app.get_window("main") {
                            let _ = window.hide();
                        }
                    }
                    "toggle_listening" => {
                        if let Some(window) = app.get_window("main") {
                            let _ = window.emit("toggle-listening", {});
                        }
                    }
                    _ => {}
                }
            }
            _ => {}
        })
        .setup(|app| {
            // Setup global shortcuts
            if let Err(e) = setup_global_shortcuts(app) {
                eprintln!("Failed to setup global shortcuts: {}", e);
            }

            // Show the main window for development/testing
            if let Some(window) = app.get_window("main") {
                let _ = window.show();
                let _ = window.set_focus();
            }

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            toggle_listening,
            start_screen_capture,
            start_audio_capture,
            send_to_ai,
            save_session_data
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
