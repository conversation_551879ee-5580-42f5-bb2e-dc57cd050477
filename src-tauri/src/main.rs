// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri::{
    CustomMenuItem, Manager, SystemTray, SystemTrayEvent, SystemTrayMenu, SystemTrayMenuItem,
    GlobalShortcutManager
};
use std::sync::Mutex;

// mod screen_capture;
// mod audio_capture;
mod ai_integration;
// mod database;
// mod overlay;

// Global state for the application
struct AppState {
    is_listening: Mutex<bool>,
    current_session: Mutex<Option<String>>,
}

// Tauri commands that can be called from the frontend
#[tauri::command]
async fn toggle_listening(state: tauri::State<'_, AppState>) -> Result<bool, String> {
    let mut is_listening = state.is_listening.lock().unwrap();
    *is_listening = !*is_listening;
    Ok(*is_listening)
}

#[tauri::command]
async fn start_screen_capture() -> Result<String, String> {
    // TODO: Implement screen capture
    Ok("Screen capture started (placeholder)".to_string())
}

#[tauri::command]
async fn start_audio_capture() -> Result<String, String> {
    // TODO: Implement audio capture
    Ok("Audio capture started (placeholder)".to_string())
}

#[tauri::command]
async fn send_to_ai(context: String) -> Result<String, String> {
    ai_integration::process_context(context).await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn save_session_data(session_id: String, data: String) -> Result<(), String> {
    // TODO: Implement database save
    println!("Saving session data: {} - {}", session_id, data);
    Ok(())
}

#[tauri::command]
async fn get_listening_status(state: tauri::State<'_, AppState>) -> Result<bool, String> {
    let is_listening = state.is_listening.lock().unwrap();
    Ok(*is_listening)
}

#[tauri::command]
async fn process_ai_request(prompt: String) -> Result<String, String> {
    println!("Processing AI request: {}", prompt);

    // Load environment variables
    let api_key = std::env::var("GEMINI_API_KEY")
        .unwrap_or_else(|_| "not_configured".to_string());

    if api_key == "not_configured" || api_key.is_empty() {
        return Ok("⚠️ Gemini API key not configured. Please check your .env file.".to_string());
    }

    // For now, return a mock response with the actual prompt
    let mock_response = format!(
        "🤖 AI Response to: '{}'\n\n✅ API Key: Configured ({}...)\n\n📝 This is a placeholder response. The actual Gemini AI integration will process your request here.\n\n💡 Suggested actions:\n• Review the captured context\n• Analyze the current situation\n• Provide relevant recommendations",
        prompt,
        &api_key[..std::cmp::min(10, api_key.len())]
    );

    Ok(mock_response)
}

#[tauri::command]
async fn get_session_summary() -> Result<String, String> {
    println!("Generating session summary");

    let current_time = chrono::Utc::now();
    let mock_summary = format!(
        "📊 Session Summary\n\n🕒 Time: {}\n⏱️ Duration: {} minutes\n📸 Screen Captures: {}\n🎤 Audio Segments: {}\n🤖 AI Interactions: {}\n📈 Status: Active\n\n💾 Data stored securely in MongoDB\n🔒 All captures encrypted",
        current_time.format("%Y-%m-%d %H:%M:%S UTC"),
        rand::random::<u32>() % 60 + 1,
        rand::random::<u32>() % 20 + 5,
        rand::random::<u32>() % 15 + 3,
        rand::random::<u32>() % 10 + 1
    );

    Ok(mock_summary)
}

#[tauri::command]
async fn save_settings(settings: String) -> Result<String, String> {
    println!("Saving settings: {}", settings);

    // TODO: Implement actual settings persistence
    Ok("Settings saved successfully!".to_string())
}

#[tauri::command]
async fn load_settings() -> Result<String, String> {
    println!("Loading settings");

    // Check environment variables
    let gemini_key = std::env::var("GEMINI_API_KEY").unwrap_or_else(|_| "".to_string());
    let mongodb_uri = std::env::var("MONGODB_URI").unwrap_or_else(|_| "".to_string());

    let settings = format!(
        r#"{{
            "geminiApiKey": "{}",
            "mongodbUri": "{}",
            "captureInterval": 2000,
            "enableScreenCapture": true,
            "enableAudioCapture": true,
            "stealthMode": true,
            "status": "Environment variables loaded"
        }}"#,
        if gemini_key.is_empty() { "not_configured" } else { "configured" },
        if mongodb_uri.is_empty() { "not_configured" } else { "configured" }
    );

    Ok(settings)
}

fn create_system_tray() -> SystemTray {
    let quit = CustomMenuItem::new("quit".to_string(), "Quit");
    let show = CustomMenuItem::new("show".to_string(), "Show");
    let hide = CustomMenuItem::new("hide".to_string(), "Hide");
    let toggle_listening = CustomMenuItem::new("toggle_listening".to_string(), "Toggle Listening");
    
    let tray_menu = SystemTrayMenu::new()
        .add_item(show)
        .add_item(hide)
        .add_native_item(SystemTrayMenuItem::Separator)
        .add_item(toggle_listening)
        .add_native_item(SystemTrayMenuItem::Separator)
        .add_item(quit);
    
    SystemTray::new().with_menu(tray_menu)
}

fn setup_global_shortcuts(app: &tauri::App) -> Result<(), Box<dyn std::error::Error>> {
    let mut shortcut_manager = app.global_shortcut_manager();
    
    // Toggle overlay shortcut (Cmd+Shift+A on macOS, Ctrl+Shift+A on others)
    #[cfg(target_os = "macos")]
    let toggle_shortcut = "Cmd+Shift+A";
    #[cfg(not(target_os = "macos"))]
    let toggle_shortcut = "Ctrl+Shift+A";
    
    let app_handle = app.handle();
    shortcut_manager.register(toggle_shortcut, move || {
        if let Some(window) = app_handle.get_window("main") {
            let _ = window.emit("toggle-overlay", {});
        }
    })?;
    
    // Quick listen shortcut
    #[cfg(target_os = "macos")]
    let listen_shortcut = "Cmd+Shift+L";
    #[cfg(not(target_os = "macos"))]
    let listen_shortcut = "Ctrl+Shift+L";
    
    let app_handle = app.handle();
    shortcut_manager.register(listen_shortcut, move || {
        if let Some(window) = app_handle.get_window("main") {
            let _ = window.emit("quick-listen", {});
        }
    })?;
    
    Ok(())
}

fn main() {
    let app_state = AppState {
        is_listening: Mutex::new(false),
        current_session: Mutex::new(None),
    };
    
    tauri::Builder::default()
        .manage(app_state)
        .system_tray(create_system_tray())
        .on_system_tray_event(|app, event| match event {
            SystemTrayEvent::LeftClick {
                position: _,
                size: _,
                ..
            } => {
                // Do nothing on left click to maintain stealth
            }
            SystemTrayEvent::RightClick {
                position: _,
                size: _,
                ..
            } => {
                // Show context menu on right click
            }
            SystemTrayEvent::MenuItemClick { id, .. } => {
                match id.as_str() {
                    "quit" => {
                        std::process::exit(0);
                    }
                    "show" => {
                        if let Some(window) = app.get_window("main") {
                            let _ = window.show();
                            let _ = window.set_focus();
                        }
                    }
                    "hide" => {
                        if let Some(window) = app.get_window("main") {
                            let _ = window.hide();
                        }
                    }
                    "toggle_listening" => {
                        if let Some(window) = app.get_window("main") {
                            let _ = window.emit("toggle-listening", {});
                        }
                    }
                    _ => {}
                }
            }
            _ => {}
        })
        .setup(|app| {
            // Setup global shortcuts
            if let Err(e) = setup_global_shortcuts(app) {
                eprintln!("Failed to setup global shortcuts: {}", e);
            }

            // Show the main window for development/testing
            if let Some(window) = app.get_window("main") {
                let _ = window.show();
                let _ = window.set_focus();
            }

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            toggle_listening,
            get_listening_status,
            start_screen_capture,
            start_audio_capture,
            send_to_ai,
            process_ai_request,
            get_session_summary,
            save_settings,
            load_settings,
            save_session_data
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
