use screenshots::Screen;
use image::{<PERSON><PERSON><PERSON><PERSON>, <PERSON>g<PERSON>};
use tesseract::Tesseract;
use std::error::Error;
use std::sync::Arc;
use tokio::sync::Mutex;
use base64::{Engine as _, engine::general_purpose};
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct ScreenCapture {
    pub timestamp: String,
    pub image_data: String, // Base64 encoded
    pub extracted_text: String,
    pub screen_info: ScreenInfo,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ScreenInfo {
    pub width: u32,
    pub height: u32,
    pub scale_factor: f64,
    pub display_id: u32,
}

pub struct ScreenCaptureManager {
    tesseract: Arc<Mutex<Tesseract>>,
    capture_active: Arc<Mutex<bool>>,
}

impl ScreenCaptureManager {
    pub fn new() -> Result<Self, Box<dyn Error>> {
        let tesseract = Tesseract::new(None, Some("eng"))?;
        
        Ok(ScreenCaptureManager {
            tesseract: Arc::new(Mutex::new(tesseract)),
            capture_active: Arc::new(Mutex::new(false)),
        })
    }

    pub async fn start_capture(&self) -> Result<String, Box<dyn Error>> {
        let mut active = self.capture_active.lock().await;
        *active = true;
        
        // Start background capture task
        let tesseract = Arc::clone(&self.tesseract);
        let capture_active = Arc::clone(&self.capture_active);
        
        tokio::spawn(async move {
            while *capture_active.lock().await {
                if let Err(e) = Self::capture_and_process(Arc::clone(&tesseract)).await {
                    eprintln!("Screen capture error: {}", e);
                }
                
                // Wait before next capture (configurable interval)
                tokio::time::sleep(tokio::time::Duration::from_millis(2000)).await;
            }
        });

        Ok("Screen capture started".to_string())
    }

    pub async fn stop_capture(&self) -> Result<String, Box<dyn Error>> {
        let mut active = self.capture_active.lock().await;
        *active = false;
        Ok("Screen capture stopped".to_string())
    }

    async fn capture_and_process(tesseract: Arc<Mutex<Tesseract>>) -> Result<ScreenCapture, Box<dyn Error>> {
        // Get all screens
        let screens = Screen::all()?;
        
        // For now, capture the primary screen (index 0)
        let screen = &screens[0];
        
        // Capture screenshot
        let image = screen.capture()?;
        
        // Convert to base64
        let mut buffer = Vec::new();
        image.save(&mut std::io::Cursor::new(&mut buffer), image::ImageFormat::Png)?;
        let image_data = general_purpose::STANDARD.encode(&buffer);
        
        // Extract text using OCR
        let extracted_text = Self::extract_text_from_image(&image, tesseract).await?;
        
        let capture = ScreenCapture {
            timestamp: chrono::Utc::now().to_rfc3339(),
            image_data,
            extracted_text,
            screen_info: ScreenInfo {
                width: image.width(),
                height: image.height(),
                scale_factor: 1.0, // TODO: Get actual scale factor
                display_id: 0, // TODO: Get actual display ID
            },
        };

        // Send to AI processing pipeline
        Self::send_to_ai_pipeline(&capture).await?;

        Ok(capture)
    }

    async fn extract_text_from_image(
        image: &ImageBuffer<Rgba<u8>, Vec<u8>>,
        tesseract: Arc<Mutex<Tesseract>>
    ) -> Result<String, Box<dyn Error>> {
        // Convert image to format Tesseract can use
        let width = image.width();
        let height = image.height();
        let raw_data: Vec<u8> = image.pixels()
            .flat_map(|pixel| vec![pixel[0], pixel[1], pixel[2]]) // RGB only
            .collect();

        let mut tess = tesseract.lock().await;
        tess.set_image(&raw_data, width as i32, height as i32, 3, (width * 3) as i32)?;
        
        let text = tess.get_text()?;
        Ok(text.trim().to_string())
    }

    async fn send_to_ai_pipeline(capture: &ScreenCapture) -> Result<(), Box<dyn Error>> {
        // This would send the capture data to the AI processing pipeline
        // For now, just log it
        println!("Captured screen with {} characters of text", capture.extracted_text.len());
        
        // TODO: Send to AI integration module
        // crate::ai_integration::process_screen_capture(capture).await?;
        
        Ok(())
    }

    pub async fn capture_specific_window(window_title: &str) -> Result<ScreenCapture, Box<dyn Error>> {
        // TODO: Implement window-specific capture
        // This would be useful for focusing on specific applications
        Err("Window-specific capture not yet implemented".into())
    }

    pub async fn capture_region(x: u32, y: u32, width: u32, height: u32) -> Result<ScreenCapture, Box<dyn Error>> {
        // TODO: Implement region-specific capture
        // This would be useful for capturing specific areas of the screen
        Err("Region-specific capture not yet implemented".into())
    }

    pub async fn get_screen_info() -> Result<Vec<ScreenInfo>, Box<dyn Error>> {
        let screens = Screen::all()?;
        let mut screen_infos = Vec::new();

        for (index, screen) in screens.iter().enumerate() {
            // Get screen dimensions
            let display_info = screen.display_info;
            
            screen_infos.push(ScreenInfo {
                width: display_info.width,
                height: display_info.height,
                scale_factor: display_info.scale_factor as f64,
                display_id: index as u32,
            });
        }

        Ok(screen_infos)
    }
}

// Global instance
static mut SCREEN_CAPTURE_MANAGER: Option<ScreenCaptureManager> = None;
static INIT: std::sync::Once = std::sync::Once::new();

pub async fn start_capture() -> Result<String, Box<dyn Error>> {
    INIT.call_once(|| {
        unsafe {
            SCREEN_CAPTURE_MANAGER = Some(ScreenCaptureManager::new().unwrap());
        }
    });

    unsafe {
        if let Some(manager) = &SCREEN_CAPTURE_MANAGER {
            manager.start_capture().await
        } else {
            Err("Failed to initialize screen capture manager".into())
        }
    }
}

pub async fn stop_capture() -> Result<String, Box<dyn Error>> {
    unsafe {
        if let Some(manager) = &SCREEN_CAPTURE_MANAGER {
            manager.stop_capture().await
        } else {
            Err("Screen capture manager not initialized".into())
        }
    }
}
