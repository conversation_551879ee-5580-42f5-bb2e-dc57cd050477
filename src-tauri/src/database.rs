use mongodb::{Client, Collection, Database};
use serde::{Deserialize, Serialize};
use std::error::Error;
use std::sync::Arc;
use tokio::sync::Mutex;
use chrono::{DateTime, Utc};
use uuid::Uuid;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct UserProfile {
    #[serde(rename = "_id")]
    pub id: String,
    pub username: String,
    pub email: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub preferences: UserPreferences,
    pub subscription_tier: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct UserPreferences {
    pub capture_interval_ms: u64,
    pub enable_screen_capture: bool,
    pub enable_audio_capture: bool,
    pub enable_ocr: bool,
    pub auto_start_listening: bool,
    pub save_session_history: bool,
    pub overlay_opacity: f32,
    pub hotkeys_enabled: bool,
    pub stealth_mode: bool,
}

#[derive(Debug, Serialize, Deserialize, <PERSON>lone)]
pub struct SessionRecord {
    #[serde(rename = "_id")]
    pub id: String,
    pub user_id: String,
    pub start_time: DateTime<Utc>,
    pub end_time: Option<DateTime<Utc>>,
    pub duration_ms: Option<u64>,
    pub captures: Vec<CaptureRecord>,
    pub ai_responses: Vec<AIResponseRecord>,
    pub user_actions: Vec<UserActionRecord>,
    pub metadata: SessionMetadata,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CaptureRecord {
    pub id: String,
    pub timestamp: DateTime<Utc>,
    pub capture_type: String, // "screen", "audio", "combined"
    pub data_size: u64,
    pub extracted_text: Option<String>,
    pub transcription: Option<String>,
    pub file_path: Option<String>, // For large files stored separately
    pub metadata: std::collections::HashMap<String, String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AIResponseRecord {
    pub id: String,
    pub timestamp: DateTime<Utc>,
    pub prompt: String,
    pub response: String,
    pub confidence: f32,
    pub processing_time_ms: u64,
    pub model_version: String,
    pub context_items_count: u32,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct UserActionRecord {
    pub id: String,
    pub timestamp: DateTime<Utc>,
    pub action_type: String, // "copy", "listen_start", "listen_stop", "settings_change"
    pub data: serde_json::Value,
    pub session_context: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SessionMetadata {
    pub total_captures: u32,
    pub total_ai_responses: u32,
    pub total_user_actions: u32,
    pub average_response_time_ms: f64,
    pub primary_activity: Option<String>,
    pub tags: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DocumentMetadata {
    #[serde(rename = "_id")]
    pub id: String,
    pub user_id: String,
    pub filename: String,
    pub file_type: String,
    pub file_size: u64,
    pub upload_time: DateTime<Utc>,
    pub processed: bool,
    pub extracted_text: Option<String>,
    pub summary: Option<String>,
    pub tags: Vec<String>,
}

pub struct DatabaseManager {
    client: Option<Client>,
    database: Option<Database>,
    connection_string: Arc<Mutex<Option<String>>>,
}

impl DatabaseManager {
    pub fn new() -> Self {
        DatabaseManager {
            client: None,
            database: None,
            connection_string: Arc::new(Mutex::new(None)),
        }
    }

    pub async fn connect(&mut self, connection_string: String) -> Result<(), Box<dyn Error>> {
        let client = Client::with_uri_str(&connection_string).await?;
        let database = client.database("cluely");
        
        // Test the connection
        database.run_command(mongodb::bson::doc! {"ping": 1}, None).await?;
        
        self.client = Some(client);
        self.database = Some(database);
        
        let mut conn_str = self.connection_string.lock().await;
        *conn_str = Some(connection_string);
        
        // Create indexes
        self.create_indexes().await?;
        
        Ok(())
    }

    async fn create_indexes(&self) -> Result<(), Box<dyn Error>> {
        if let Some(db) = &self.database {
            // Create indexes for better query performance
            let sessions: Collection<SessionRecord> = db.collection("sessions");
            sessions.create_index(
                mongodb::bson::doc! {"user_id": 1, "start_time": -1},
                None
            ).await?;

            let captures: Collection<CaptureRecord> = db.collection("captures");
            captures.create_index(
                mongodb::bson::doc! {"timestamp": -1},
                None
            ).await?;

            let users: Collection<UserProfile> = db.collection("users");
            users.create_index(
                mongodb::bson::doc! {"username": 1},
                None
            ).await?;
        }
        
        Ok(())
    }

    pub async fn save_session(&self, session_id: String, data: String) -> Result<(), Box<dyn Error>> {
        if let Some(db) = &self.database {
            // Parse the session data
            let session_data: serde_json::Value = serde_json::from_str(&data)?;
            
            // Create or update session record
            let sessions: Collection<SessionRecord> = db.collection("sessions");
            
            let session_record = SessionRecord {
                id: session_id.clone(),
                user_id: "default_user".to_string(), // TODO: Get actual user ID
                start_time: Utc::now(),
                end_time: None,
                duration_ms: None,
                captures: vec![], // TODO: Parse from data
                ai_responses: vec![], // TODO: Parse from data
                user_actions: vec![], // TODO: Parse from data
                metadata: SessionMetadata {
                    total_captures: 0,
                    total_ai_responses: 0,
                    total_user_actions: 0,
                    average_response_time_ms: 0.0,
                    primary_activity: None,
                    tags: vec![],
                },
            };

            sessions.insert_one(session_record, None).await?;
        }
        
        Ok(())
    }

    pub async fn get_user_sessions(&self, user_id: &str, limit: i64) -> Result<Vec<SessionRecord>, Box<dyn Error>> {
        if let Some(db) = &self.database {
            let sessions: Collection<SessionRecord> = db.collection("sessions");
            
            let filter = mongodb::bson::doc! {"user_id": user_id};
            let options = mongodb::options::FindOptions::builder()
                .sort(mongodb::bson::doc! {"start_time": -1})
                .limit(limit)
                .build();
            
            let mut cursor = sessions.find(filter, options).await?;
            let mut results = Vec::new();
            
            while cursor.advance().await? {
                results.push(cursor.deserialize_current()?);
            }
            
            Ok(results)
        } else {
            Err("Database not connected".into())
        }
    }

    pub async fn save_capture(&self, capture: CaptureRecord) -> Result<(), Box<dyn Error>> {
        if let Some(db) = &self.database {
            let captures: Collection<CaptureRecord> = db.collection("captures");
            captures.insert_one(capture, None).await?;
        }
        Ok(())
    }

    pub async fn save_ai_response(&self, response: AIResponseRecord) -> Result<(), Box<dyn Error>> {
        if let Some(db) = &self.database {
            let responses: Collection<AIResponseRecord> = db.collection("ai_responses");
            responses.insert_one(response, None).await?;
        }
        Ok(())
    }

    pub async fn save_user_action(&self, action: UserActionRecord) -> Result<(), Box<dyn Error>> {
        if let Some(db) = &self.database {
            let actions: Collection<UserActionRecord> = db.collection("user_actions");
            actions.insert_one(action, None).await?;
        }
        Ok(())
    }

    pub async fn get_user_profile(&self, user_id: &str) -> Result<Option<UserProfile>, Box<dyn Error>> {
        if let Some(db) = &self.database {
            let users: Collection<UserProfile> = db.collection("users");
            let filter = mongodb::bson::doc! {"_id": user_id};
            let result = users.find_one(filter, None).await?;
            Ok(result)
        } else {
            Err("Database not connected".into())
        }
    }

    pub async fn create_user_profile(&self, username: String, email: Option<String>) -> Result<UserProfile, Box<dyn Error>> {
        if let Some(db) = &self.database {
            let users: Collection<UserProfile> = db.collection("users");
            
            let user_profile = UserProfile {
                id: Uuid::new_v4().to_string(),
                username,
                email,
                created_at: Utc::now(),
                updated_at: Utc::now(),
                preferences: UserPreferences {
                    capture_interval_ms: 2000,
                    enable_screen_capture: true,
                    enable_audio_capture: true,
                    enable_ocr: true,
                    auto_start_listening: false,
                    save_session_history: true,
                    overlay_opacity: 0.85,
                    hotkeys_enabled: true,
                    stealth_mode: true,
                },
                subscription_tier: "free".to_string(),
            };

            users.insert_one(&user_profile, None).await?;
            Ok(user_profile)
        } else {
            Err("Database not connected".into())
        }
    }

    pub async fn update_user_preferences(&self, user_id: &str, preferences: UserPreferences) -> Result<(), Box<dyn Error>> {
        if let Some(db) = &self.database {
            let users: Collection<UserProfile> = db.collection("users");
            let filter = mongodb::bson::doc! {"_id": user_id};
            let update = mongodb::bson::doc! {
                "$set": {
                    "preferences": mongodb::bson::to_bson(&preferences)?,
                    "updated_at": Utc::now()
                }
            };
            
            users.update_one(filter, update, None).await?;
        }
        Ok(())
    }

    pub async fn save_document_metadata(&self, metadata: DocumentMetadata) -> Result<(), Box<dyn Error>> {
        if let Some(db) = &self.database {
            let documents: Collection<DocumentMetadata> = db.collection("documents");
            documents.insert_one(metadata, None).await?;
        }
        Ok(())
    }

    pub async fn search_sessions(&self, user_id: &str, query: &str, limit: i64) -> Result<Vec<SessionRecord>, Box<dyn Error>> {
        if let Some(db) = &self.database {
            let sessions: Collection<SessionRecord> = db.collection("sessions");
            
            // Simple text search - in production, you'd want full-text search
            let filter = mongodb::bson::doc! {
                "user_id": user_id,
                "$or": [
                    {"captures.extracted_text": {"$regex": query, "$options": "i"}},
                    {"ai_responses.response": {"$regex": query, "$options": "i"}}
                ]
            };
            
            let options = mongodb::options::FindOptions::builder()
                .sort(mongodb::bson::doc! {"start_time": -1})
                .limit(limit)
                .build();
            
            let mut cursor = sessions.find(filter, options).await?;
            let mut results = Vec::new();
            
            while cursor.advance().await? {
                results.push(cursor.deserialize_current()?);
            }
            
            Ok(results)
        } else {
            Err("Database not connected".into())
        }
    }

    pub async fn cleanup_old_sessions(&self, days_old: i64) -> Result<u64, Box<dyn Error>> {
        if let Some(db) = &self.database {
            let sessions: Collection<SessionRecord> = db.collection("sessions");
            
            let cutoff_date = Utc::now() - chrono::Duration::days(days_old);
            let filter = mongodb::bson::doc! {"start_time": {"$lt": cutoff_date}};
            
            let result = sessions.delete_many(filter, None).await?;
            Ok(result.deleted_count)
        } else {
            Err("Database not connected".into())
        }
    }
}

// Global instance
static mut DATABASE_MANAGER: Option<DatabaseManager> = None;
static INIT: std::sync::Once = std::sync::Once::new();

pub async fn save_session(session_id: String, data: String) -> Result<(), Box<dyn Error>> {
    INIT.call_once(|| {
        unsafe {
            DATABASE_MANAGER = Some(DatabaseManager::new());
        }
    });

    unsafe {
        if let Some(manager) = &DATABASE_MANAGER {
            manager.save_session(session_id, data).await
        } else {
            Err("Failed to initialize database manager".into())
        }
    }
}

pub async fn connect_database(connection_string: String) -> Result<(), Box<dyn Error>> {
    unsafe {
        if let Some(manager) = &mut DATABASE_MANAGER {
            manager.connect(connection_string).await
        } else {
            Err("Database manager not initialized".into())
        }
    }
}
