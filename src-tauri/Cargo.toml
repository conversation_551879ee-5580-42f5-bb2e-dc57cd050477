[package]
name = "cluely-ai-assistant"
version = "0.1.0"
description = "A stealth AI assistant that provides real-time contextual suggestions"
authors = ["Your Name <<EMAIL>>"]
license = "MIT"
repository = "https://github.com/yourusername/cluely-ai-assistant"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "1.5.1", features = [] }

[dependencies]
tauri = { version = "1.5.4", features = [
  "fs-copy-file",
  "fs-create-dir",
  "fs-exists",
  "fs-read-dir",
  "fs-read-file",
  "fs-remove-dir",
  "fs-remove-file",
  "fs-rename-file",
  "fs-write-file",
  "global-shortcut-all",
  "os-all",
  "path-all",
  "shell-open",
  "system-tray",
  "window-close",
  "window-hide",
  "window-maximize",
  "window-minimize",
  "window-set-always-on-top",
  "window-set-position",
  "window-set-size",
  "window-set-title",
  "window-show",
  "window-start-dragging",
  "window-unmaximize",
  "window-unminimize"
] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.35", features = ["full"] }
reqwest = { version = "0.11", features = ["json"] }
base64 = "0.21"
uuid = { version = "1.6", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }
rand = "0.8"

[target.'cfg(target_os = "macos")'.dependencies]
cocoa = "0.25"
objc = "0.2"

[target.'cfg(target_os = "windows")'.dependencies]
winapi = { version = "0.3", features = ["winuser", "wingdi"] }

[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]
