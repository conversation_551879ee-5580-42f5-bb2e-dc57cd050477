[package]
name = "cluely-ai-assistant"
version = "0.1.0"
description = "A stealth AI assistant that provides real-time contextual suggestions"
authors = ["Your Name <<EMAIL>>"]
license = "MIT"
repository = "https://github.com/yourusername/cluely-ai-assistant"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "1.5.1", features = [] }

[dependencies]
tauri = { version = "1.5.4", features = ["api-all", "system-tray", "global-shortcut", "window-all"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.35", features = ["full"] }
reqwest = { version = "0.11", features = ["json"] }
mongodb = "2.8"
screenshots = "0.7"
image = "0.24"
tesseract = "0.13"
cpal = "0.15"
hound = "3.5"
whisper-rs = "0.10"
global-hotkey = "0.4"
rdev = "0.4"
base64 = "0.21"
uuid = { version = "1.6", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }
aes-gcm = "0.10"
rand = "0.8"

[target.'cfg(target_os = "macos")'.dependencies]
cocoa = "0.25"
objc = "0.2"

[target.'cfg(target_os = "windows")'.dependencies]
winapi = { version = "0.3", features = ["winuser", "wingdi"] }

[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]
