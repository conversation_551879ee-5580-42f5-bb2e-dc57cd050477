{"build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "devPath": "http://localhost:1420", "distDir": "../dist", "withGlobalTauri": false}, "package": {"productName": "Cluely AI Assistant", "version": "0.1.0"}, "tauri": {"allowlist": {"all": false, "shell": {"all": false, "open": true}, "window": {"all": false, "close": true, "hide": true, "show": true, "maximize": true, "minimize": true, "unmaximize": true, "unminimize": true, "startDragging": true, "setAlwaysOnTop": true, "setPosition": true, "setSize": true, "setTitle": true}, "globalShortcut": {"all": true}, "fs": {"all": false, "readFile": true, "writeFile": true, "readDir": true, "copyFile": true, "createDir": true, "removeDir": true, "removeFile": true, "renameFile": true, "exists": true}, "path": {"all": true}, "os": {"all": true}}, "bundle": {"active": true, "targets": "all", "identifier": "com.cluely.ai-assistant", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "security": {"csp": null}, "windows": [{"fullscreen": false, "resizable": true, "title": "Cluely AI Assistant", "width": 400, "height": 600, "visible": false, "transparent": true, "decorations": false, "alwaysOnTop": true, "skipTaskbar": true, "center": true}], "systemTray": {"iconPath": "icons/icon.png", "iconAsTemplate": true, "menuOnLeftClick": false}}}