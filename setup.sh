#!/bin/bash

# Cluely AI Assistant Setup Script
# This script helps set up the development environment for Cluely AI Assistant

set -e

echo "🚀 Setting up Cluely AI Assistant..."
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Node.js
    if command_exists node; then
        NODE_VERSION=$(node --version)
        print_success "Node.js found: $NODE_VERSION"
    else
        print_error "Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/"
        exit 1
    fi
    
    # Check npm
    if command_exists npm; then
        NPM_VERSION=$(npm --version)
        print_success "npm found: $NPM_VERSION"
    else
        print_error "npm is not installed. Please install npm."
        exit 1
    fi
    
    # Check Rust
    if command_exists rustc; then
        RUST_VERSION=$(rustc --version)
        print_success "Rust found: $RUST_VERSION"
    else
        print_error "Rust is not installed. Please install Rust from https://rustup.rs/"
        exit 1
    fi
    
    # Check Cargo
    if command_exists cargo; then
        CARGO_VERSION=$(cargo --version)
        print_success "Cargo found: $CARGO_VERSION"
    else
        print_error "Cargo is not installed. Please install Rust toolchain."
        exit 1
    fi
    
    # Check MongoDB (optional)
    if command_exists mongod; then
        print_success "MongoDB found"
    else
        print_warning "MongoDB not found. You can install it later or use MongoDB Atlas."
    fi
}

# Install Node.js dependencies
install_node_dependencies() {
    print_status "Installing Node.js dependencies..."
    
    if [ -f "package.json" ]; then
        npm install
        print_success "Node.js dependencies installed"
    else
        print_error "package.json not found. Are you in the correct directory?"
        exit 1
    fi
}

# Install Rust dependencies
install_rust_dependencies() {
    print_status "Installing Rust dependencies..."
    
    if [ -d "src-tauri" ]; then
        cd src-tauri
        cargo build
        cd ..
        print_success "Rust dependencies installed"
    else
        print_error "src-tauri directory not found. Are you in the correct directory?"
        exit 1
    fi
}

# Setup environment file
setup_environment() {
    print_status "Setting up environment configuration..."
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            print_success "Created .env file from .env.example"
            print_warning "Please edit .env file and add your API keys and configuration"
        else
            print_error ".env.example not found"
            exit 1
        fi
    else
        print_warning ".env file already exists. Skipping..."
    fi
}

# Install Tauri CLI
install_tauri_cli() {
    print_status "Installing Tauri CLI..."
    
    if command_exists cargo; then
        cargo install tauri-cli
        print_success "Tauri CLI installed"
    else
        print_error "Cargo not found. Cannot install Tauri CLI."
        exit 1
    fi
}

# Setup MongoDB (optional)
setup_mongodb() {
    print_status "MongoDB setup..."
    
    if command_exists mongod; then
        print_success "MongoDB is already installed"
        
        # Check if MongoDB is running
        if pgrep mongod > /dev/null; then
            print_success "MongoDB is running"
        else
            print_warning "MongoDB is not running. You may need to start it manually."
            echo "  macOS: brew services start mongodb/brew/mongodb-community"
            echo "  Linux: sudo systemctl start mongod"
            echo "  Windows: net start MongoDB"
        fi
    else
        print_warning "MongoDB not found. Installation options:"
        echo "  macOS: brew install mongodb/brew/mongodb-community"
        echo "  Linux: Follow instructions at https://docs.mongodb.com/manual/installation/"
        echo "  Windows: Download from https://www.mongodb.com/try/download/community"
        echo "  Or use MongoDB Atlas (cloud): https://www.mongodb.com/atlas"
    fi
}

# Check system permissions
check_permissions() {
    print_status "Checking system permissions..."
    
    case "$(uname -s)" in
        Darwin*)
            print_warning "macOS detected. You will need to grant the following permissions:"
            echo "  1. Screen Recording: System Preferences > Security & Privacy > Privacy > Screen Recording"
            echo "  2. Microphone: System Preferences > Security & Privacy > Privacy > Microphone"
            echo "  3. Accessibility: System Preferences > Security & Privacy > Privacy > Accessibility"
            ;;
        Linux*)
            print_warning "Linux detected. You may need to configure:"
            echo "  1. Screen capture permissions (depends on desktop environment)"
            echo "  2. Audio permissions (PulseAudio/ALSA configuration)"
            ;;
        MINGW*|CYGWIN*|MSYS*)
            print_warning "Windows detected. You may need to grant:"
            echo "  1. Microphone permissions: Windows Settings > Privacy > Microphone"
            echo "  2. Screen capture is usually available by default"
            ;;
        *)
            print_warning "Unknown operating system. Please check permissions manually."
            ;;
    esac
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p logs
    mkdir -p data
    mkdir -p temp
    
    print_success "Directories created"
}

# Verify installation
verify_installation() {
    print_status "Verifying installation..."
    
    # Check if we can build the project
    if npm run build > /dev/null 2>&1; then
        print_success "Frontend build successful"
    else
        print_warning "Frontend build failed. Check your configuration."
    fi
    
    # Check if Tauri can build
    if cd src-tauri && cargo check > /dev/null 2>&1; then
        cd ..
        print_success "Rust backend check successful"
    else
        cd ..
        print_warning "Rust backend check failed. Check your dependencies."
    fi
}

# Main setup function
main() {
    echo "Starting setup process..."
    echo
    
    check_prerequisites
    echo
    
    install_node_dependencies
    echo
    
    install_rust_dependencies
    echo
    
    setup_environment
    echo
    
    install_tauri_cli
    echo
    
    setup_mongodb
    echo
    
    check_permissions
    echo
    
    create_directories
    echo
    
    verify_installation
    echo
    
    print_success "Setup completed successfully! 🎉"
    echo
    echo "Next steps:"
    echo "1. Edit the .env file and add your API keys"
    echo "2. Start MongoDB if using local installation"
    echo "3. Grant necessary system permissions"
    echo "4. Run 'npm run tauri:dev' to start development"
    echo
    echo "For more information, see README.md"
}

# Run main function
main "$@"
